from django.dispatch import receiver
from django.db.models.signals import post_save
from deals import signals as deals_signals
from disputes import signals as disputes_signals
from chat.models import Message


@receiver(post_save, sender=Message)
def update_deal_last_message(sender, instance, created, **kwargs):
    if created:
        deal = instance.deal

        deal.last_message_at = instance.created_at

        if instance.sender == deal.talent.user:
            # if talent send message, then it have to be unread for client only
            deal.client_unread_count += 1
        else:
            # if client send message, then it have to be unread for talent only
            deal.talent_unread_count += 1

        # deal save will trigger a stream
        deal.save()


@receiver(post_save, sender=Message)
def update_notification_schedule(sender, instance, created, **kwargs):
    if created:
        deal = instance.deal

        if instance.sender == deal.talent.user:
            user = deal.client.user
        else:
            user = deal.talent.user

        notification, created = deal.notifications.get_or_create(user=user)
        notification.last_message_at = instance.created_at
        notification.save()


@receiver(deals_signals.deal_hiring_requested)
def post_a_hiring_message(sender, deal, **kwargs):
    if deal.job.no_done_milestones:
        template_url = "chat/components/custom_messages/deal_hiring_requested.html"
    else:
        template_url = "chat/components/custom_messages/deal_re_hiring_requested.html"
    Message.objects.create(
        deal=deal,
        template=template_url,
    )


@receiver(deals_signals.deal_hiring_canceled)
def post_a_hiring_cancel_message(sender, deal, **kwargs):
    if deal.job.no_done_milestones:
        template_url = "chat/components/custom_messages/deal_hiring_canceled.html"
    else:
        template_url = "chat/components/custom_messages/deal_re_hiring_canceled.html"
    Message.objects.create(
        deal=deal,
        template=template_url,
    )


@receiver(deals_signals.deal_hiring_rejected)
def post_a_hiring_reject_message(sender, deal, **kwargs):
    if deal.job.no_done_milestones:
        template_url = "chat/components/custom_messages/deal_hiring_rejected.html"
    else:
        template_url = "chat/components/custom_messages/deal_re_hiring_rejected.html"
    Message.objects.create(
        deal=deal,
        template=template_url,
    )


@receiver(deals_signals.deal_hiring_accepted)
def post_a_hiring_accept_message(sender, deal, **kwargs):
    if deal.job.no_done_milestones:
        template_url = "chat/components/custom_messages/deal_hiring_accepted.html"
    else:
        template_url = "chat/components/custom_messages/deal_re_hiring_accepted.html"
    Message.objects.create(
        deal=deal,
        template=template_url,
    )


@receiver(deals_signals.deal_completed)
def post_a_review_request_message(sender, deal, **kwargs):
    """
    Job is done when the client completes the job or the milestone is canceled (by admin via Dispute)
    """
    Message.objects.create(
        deal=deal,
        template="chat/components/custom_messages/review_request.html",
    )


@receiver(disputes_signals.dispute_accepted)
def post_a_review_request_message_on_dispute(sender, dispute, **kwargs):
    """
    Job is done when the client completes the job or the milestone is canceled (by admin via Dispute)
    We do not need to allow to review if the job was simply canceled after a dispute rejection.
    """
    Message.objects.create(
        deal=dispute.deal,
        template="chat/components/custom_messages/review_request.html",
    )
