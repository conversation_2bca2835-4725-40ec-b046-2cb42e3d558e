# Lesson 17: Deal & Contract System 🤝

## 🎯 Learning Objectives
- Deal lifecycle management
- Contract creation and terms
- Milestone-based payments
- Dispute resolution workflow
- Performance tracking

## 📋 Deal Model Structure

```python
class Deal(models.Model):
    class StatusChoices(models.TextChoices):
        PENDING = "pending", "Pending"
        ACTIVE = "active", "Active"
        COMPLETED = "completed", "Completed"
        CANCELLED = "cancelled", "Cancelled"
        DISPUTED = "disputed", "Disputed"
    
    # Core relationships
    job = models.ForeignKey(Job, on_delete=models.CASCADE)
    talent = models.ForeignKey(Talent, on_delete=models.CASCADE)
    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    
    # Deal terms
    status = models.CharField(max_length=20, choices=StatusChoices.choices)
    proposal = models.TextField()
    agreed_rate = models.DecimalField(max_digits=10, decimal_places=2)
    estimated_hours = models.PositiveIntegerField()
    
    # Timeline
    start_date = models.DateField(null=True)
    end_date = models.DateField(null=True)
    actual_completion_date = models.DateField(null=True)
    
    # Communication
    last_message_at = models.DateTimeField(auto_now_add=True)
    client_unread_count = models.PositiveIntegerField(default=0)
    talent_unread_count = models.PositiveIntegerField(default=0)
```

## 💰 Milestone System

### **Milestone Model**
```python
class Milestone(models.Model):
    class StatusChoices(models.TextChoices):
        TODO = "todo", "To Do"
        IN_PROGRESS = "in_progress", "In Progress"
        SUBMITTED = "submitted", "Submitted for Review"
        APPROVED = "approved", "Approved"
        REJECTED = "rejected", "Rejected"
        PAID = "paid", "Paid"
    
    deal = models.ForeignKey(Deal, on_delete=models.CASCADE, related_name="milestones")
    title = models.CharField(max_length=255)
    description = models.TextField()
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    
    status = models.CharField(max_length=20, choices=StatusChoices.choices, default=StatusChoices.TODO)
    due_date = models.DateField()
    
    # Submission
    submitted_at = models.DateTimeField(null=True)
    submission_notes = models.TextField(blank=True)
    deliverables = models.ManyToManyField('Deliverable', blank=True)
    
    # Review
    reviewed_at = models.DateTimeField(null=True)
    review_notes = models.TextField(blank=True)
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
```

### **Milestone Management View**
```python
class MilestoneUpdateView(DealParticipantMixin, UpdateView):
    model = Milestone
    fields = ['status', 'submission_notes']
    
    def form_valid(self, form):
        milestone = form.save(commit=False)
        
        if milestone.status == Milestone.StatusChoices.SUBMITTED:
            milestone.submitted_at = timezone.now()
            
            # Notify client
            send_milestone_submission_notification.delay(milestone.id)
            
        elif milestone.status == Milestone.StatusChoices.APPROVED:
            milestone.reviewed_at = timezone.now()
            milestone.approved_by = self.request.user
            
            # Process payment
            process_milestone_payment.delay(milestone.id)
            
        milestone.save()
        return super().form_valid(form)
```

## 🔄 Deal Workflow

### **Hiring Process**
```python
class HiringView(ClientRequiredMixin, View):
    def post(self, request, deal_id):
        deal = get_object_or_404(Deal, id=deal_id, client=request.user.client)
        
        if deal.status != Deal.StatusChoices.PENDING:
            return JsonResponse({'error': 'Deal is not in pending status'})
        
        # Create contract terms
        contract_data = {
            'start_date': request.POST.get('start_date'),
            'end_date': request.POST.get('end_date'),
            'agreed_rate': request.POST.get('agreed_rate'),
            'milestones': json.loads(request.POST.get('milestones', '[]'))
        }
        
        # Update deal
        deal.status = Deal.StatusChoices.ACTIVE
        deal.start_date = contract_data['start_date']
        deal.end_date = contract_data['end_date']
        deal.agreed_rate = contract_data['agreed_rate']
        deal.hired_at = timezone.now()
        deal.save()
        
        # Create milestones
        for milestone_data in contract_data['milestones']:
            Milestone.objects.create(
                deal=deal,
                title=milestone_data['title'],
                description=milestone_data['description'],
                amount=milestone_data['amount'],
                due_date=milestone_data['due_date']
            )
        
        # Update job status
        deal.job.status = Job.StatusChoices.IN_PROGRESS
        deal.job.save()
        
        # Send notifications
        send_hiring_notification.delay(deal.id)
        
        return JsonResponse({'success': True, 'deal_id': deal.id})
```

## 📊 Deal Analytics

### **Performance Tracking**
```python
class DealAnalyticsView(DealParticipantMixin, DetailView):
    model = Deal
    template_name = "deals/analytics.html"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        deal = self.object
        
        # Progress tracking
        total_milestones = deal.milestones.count()
        completed_milestones = deal.milestones.filter(
            status=Milestone.StatusChoices.PAID
        ).count()
        
        context['progress_percentage'] = (
            (completed_milestones / total_milestones * 100) 
            if total_milestones > 0 else 0
        )
        
        # Financial summary
        context['total_budget'] = deal.milestones.aggregate(
            total=Sum('amount')
        )['total'] or 0
        
        context['paid_amount'] = deal.milestones.filter(
            status=Milestone.StatusChoices.PAID
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        # Timeline analysis
        if deal.start_date and deal.end_date:
            total_days = (deal.end_date - deal.start_date).days
            elapsed_days = (timezone.now().date() - deal.start_date).days
            context['timeline_percentage'] = min(
                (elapsed_days / total_days * 100) if total_days > 0 else 0,
                100
            )
        
        return context
```

## ⚖️ Dispute Resolution

### **Dispute Model**
```python
class Dispute(models.Model):
    class StatusChoices(models.TextChoices):
        OPEN = "open", "Open"
        IN_REVIEW = "in_review", "In Review"
        RESOLVED = "resolved", "Resolved"
        CLOSED = "closed", "Closed"
    
    deal = models.ForeignKey(Deal, on_delete=models.CASCADE)
    raised_by = models.ForeignKey(User, on_delete=models.CASCADE)
    
    title = models.CharField(max_length=255)
    description = models.TextField()
    status = models.CharField(max_length=20, choices=StatusChoices.choices)
    
    # Resolution
    resolution = models.TextField(blank=True)
    resolved_by = models.ForeignKey(
        User, on_delete=models.SET_NULL, null=True, related_name="resolved_disputes"
    )
    resolved_at = models.DateTimeField(null=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
```

### **Dispute Creation View**
```python
class DisputeCreateView(DealParticipantMixin, CreateView):
    model = Dispute
    form_class = DisputeForm
    template_name = "disputes/create.html"
    
    def form_valid(self, form):
        dispute = form.save(commit=False)
        dispute.deal = self.get_deal()
        dispute.raised_by = self.request.user
        dispute.save()
        
        # Update deal status
        dispute.deal.status = Deal.StatusChoices.DISPUTED
        dispute.deal.save()
        
        # Notify admin and other party
        send_dispute_notification.delay(dispute.id)
        
        messages.success(
            self.request,
            "Dispute has been raised. Our team will review it within 24 hours."
        )
        
        return redirect('disputes:detail', pk=dispute.pk)
```

## 🎯 Key Takeaways

1. **Complete Deal Lifecycle**: From application to completion
2. **Milestone System**: Structured payment and delivery tracking
3. **Contract Management**: Clear terms and timeline tracking
4. **Performance Analytics**: Progress and financial monitoring
5. **Dispute Resolution**: Structured conflict resolution process

## 🔗 What's Next?

**Next**: [Lesson 18: Chat & Communication](./18-chat-system.md)
