import time, logging
import turbo
from .models import Message

logger = logging.getLogger(__name__)


class MessageStream(turbo.ModelStream):
    class Meta:
        model = Message

    def on_save(self, message, created, *args, **kwargs):
        if message.is_ready:
            message.deal.stream.append(
                "chat/components/message.html",
                {
                    "message": message,
                },
                id="my-deal-messages",
            )
