{% if user.is_client and not user.client.has_payment_method %}
<div class="flex items-center p-4 mb-8 text-sm text-red-700 rounded-lg bg-red-100" role="alert">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="flex-shrink-0 inline w-5 h-5 mr-3">
        <path fill-rule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zm-1.72 6.97a.75.75 0 10-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 101.06 1.06L12 13.06l1.72 1.72a.75.75 0 101.06-1.06L13.06 12l1.72-1.72a.75.75 0 10-1.06-1.06L12 10.94l-1.72-1.72z" clip-rule="evenodd"></path>
    </svg>
    <span class="sr-only">Info</span>
    <div>
        {% if place == "my_jobs_list" %}
            <span class="font-medium"> WARNING:</span> You have no payment method added. You can not post a Job without a payment method.
        {% elif place == "talent_public_detail" %}
            <span class="font-medium"> WARNING:</span> You have no payment method added. You can not invite this talent for your jobs without a payment method.
        {% else %}
            <span class="font-medium"> WARNING:</span> You have no payment method added.
        {% endif %}
        <a
                href="{% url "finance:client_pm_setup" %}?redirect_url={{ request.path }}"
                class="font-medium underline">
            Add Payment Method
        </a>
    </div>
</div>
{% endif %}