import copy
from unittest.mock import patch
from django.test import TestCase, override_settings
from django.urls import reverse
from django.conf import settings
from django.core.exceptions import PermissionDenied
from django.core.files.uploadedfile import SimpleUploadedFile
from registration.tests.factories import UserFactory
from deals.tests.factories import DealFactory
from chat.tests.factories import MessageFactory


@override_settings(MESSAGE_ATTACHMENT_MAX_UPLOAD_SIZE=102400)
class TestMessageCreate(TestCase):
    def setUp(self) -> None:
        self.deal = DealFactory.create()

    @patch.object(PermissionDenied, "__init__", return_value=None)
    def test_participant_of_deal_error(self, mocked_exception):
        wrong_user = UserFactory.create()

        self.client.force_login(wrong_user)

        result = self.client.post(
            reverse("chat:message_create", kwargs={"deal_id": self.deal.pk}),
            data={"message": "test"},
        )
        self.assertEqual(result.status_code, 403)
        mocked_exception.assert_called_once()
        mocked_exception.assert_called_with("User is not a participant of deal")

    def test_message_empty_error(self):
        self.client.force_login(self.deal.client.user)

        result = self.client.post(
            reverse("chat:message_create", kwargs={"deal_id": self.deal.pk}),
            data={"message": "test"},
        )
        self.assertFormError(result, "form", "text", "This field is required.")

    def test_message_attachments_count_error(self):
        self.client.force_login(self.deal.client.user)

        file = SimpleUploadedFile("file.mp4", b"file_content", content_type="video/mp4")

        allowed_count = settings.CHAT_MAX_ATTACHMENTS_COUNT
        attachments = []
        for i in range(allowed_count + 1):
            attachments.append(copy.deepcopy(file))

        result = self.client.post(
            reverse("chat:message_create", kwargs={"deal_id": self.deal.pk}),
            data={
                "text": "test",
                "attachments": attachments,
            },
            format="multipart",
        )
        self.assertFormError(
            result,
            "form",
            "attachments",
            f"Too many attachments. Max {allowed_count} allowed.",
        )

    def test_message_attachments_size_error(self):
        self.client.force_login(self.deal.client.user)
        content = bytes("x" * 1024 * 200, "utf-8")  # 200kb
        file = SimpleUploadedFile("file.pdf", content, content_type="application/pdf")

        result = self.client.post(
            reverse("chat:message_create", kwargs={"deal_id": self.deal.pk}),
            data={
                "text": "test",
                "attachments": [
                    copy.deepcopy(file),
                ],
            },
            format="multipart",
        )
        self.assertFormError(
            result,
            "form",
            "attachments",
            f"File {file.name} is too large. Max {settings.MESSAGE_ATTACHMENT_MAX_UPLOAD_SIZE} allowed.",
        )

    def test_message_and_attachment_ok(self):
        self.client.force_login(self.deal.client.user)

        file = SimpleUploadedFile("file.pdf", b"qwas", content_type="application/pdf")

        result = self.client.post(
            reverse("chat:message_create", kwargs={"deal_id": self.deal.pk}),
            data={
                "text": "test",
                "attachments": [
                    copy.deepcopy(file),
                ],
            },
            format="multipart",
        )
        self.assertRedirects(
            result,
            reverse("chat:message_create", kwargs={"deal_id": self.deal.pk}),
            status_code=302,
        )


class TestMessageList(TestCase):
    def setUp(self) -> None:
        self.deal = DealFactory.create()
        self.m1 = MessageFactory.create(deal=self.deal, sender=self.deal.client.user)
        self.m2 = MessageFactory.create(deal=self.deal, sender=self.deal.talent.user)

    def test_participant_of_deal_error(self):
        # This mixin tested in TestMessageCreate.test_participant_of_deal_error()
        pass

    def test_message_list_client_ok(self):
        self.client.force_login(self.deal.client.user)

        result = self.client.get(
            reverse("chat:message_list", kwargs={"deal_id": self.deal.pk})
        )
        self.assertEqual(result.status_code, 200)
        self.assertTemplateUsed(result, "chat/message_list.html")
        self.assertEqual(len(result.context["chat_messages"]), 2)

    def test_message_list_talent_ok(self):
        self.client.force_login(self.deal.talent.user)

        result = self.client.get(
            reverse("chat:message_list", kwargs={"deal_id": self.deal.pk})
        )
        self.assertEqual(result.status_code, 200)
        self.assertTemplateUsed(result, "chat/message_list.html")
        self.assertEqual(len(result.context["chat_messages"]), 2)
