# Lesson 15: Job Posting & Management 💼

## 🎯 Learning Objectives
- Job creation and editing workflow
- Job status management
- Application handling
- Search and filtering system
- Job performance analytics

## 📝 Job Model Structure

```python
class Job(models.Model):
    class StatusChoices(models.TextChoices):
        DRAFT = "draft", "Draft"
        PUBLISHED = "published", "Published"
        IN_PROGRESS = "in_progress", "In Progress"
        COMPLETED = "completed", "Completed"
        CANCELLED = "cancelled", "Cancelled"
    
    client = models.ForeignKey(Client, on_delete=models.CASCADE)
    title = models.CharField(max_length=255)
    description = models.TextField()
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    skills = models.ManyToManyField(Skill, blank=True)
    
    budget_min = models.DecimalField(max_digits=10, decimal_places=2)
    budget_max = models.DecimalField(max_digits=10, decimal_places=2)
    duration_weeks = models.PositiveIntegerField(null=True)
    
    status = models.Char<PERSON><PERSON>(max_length=20, choices=StatusChoices.choices)
    applications_count = models.PositiveIntegerField(default=0)
    views_count = models.PositiveIntegerField(default=0)
```

## 🔍 Job Search & Filtering

### **Advanced Search View**
```python
class JobSearchView(ListView):
    model = Job
    template_name = "jobs/search.html"
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Job.objects.published()
        
        # Text search
        q = self.request.GET.get('q')
        if q:
            queryset = queryset.filter(
                Q(title__icontains=q) | Q(description__icontains=q)
            )
        
        # Filters
        category = self.request.GET.get('category')
        if category:
            queryset = queryset.filter(category__slug=category)
        
        skills = self.request.GET.getlist('skills')
        if skills:
            queryset = queryset.filter(skills__id__in=skills).distinct()
        
        # Budget range
        min_budget = self.request.GET.get('min_budget')
        max_budget = self.request.GET.get('max_budget')
        if min_budget:
            queryset = queryset.filter(budget_min__gte=min_budget)
        if max_budget:
            queryset = queryset.filter(budget_max__lte=max_budget)
        
        return queryset.select_related('client__user', 'category')
```

## 📊 Application Management

### **Application Tracking**
```python
class JobApplicationsView(ClientRequiredMixin, DetailView):
    model = Job
    template_name = "jobs/applications.html"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        applications = Deal.objects.filter(
            job=self.object,
            status=Deal.StatusChoices.PENDING
        ).select_related('talent__user')
        
        context['applications'] = applications
        context['hired_count'] = Deal.objects.filter(
            job=self.object,
            status=Deal.StatusChoices.ACTIVE
        ).count()
        
        return context
```

## 🎯 Key Takeaways

1. **Comprehensive Job Model**: Status tracking, budget ranges, skill requirements
2. **Advanced Search**: Text search with multiple filters
3. **Application Management**: Track and manage talent applications
4. **Performance Analytics**: View counts and application metrics
5. **Client Dashboard**: Centralized job management interface

## 🔗 What's Next?

**Next**: [Lesson 16: Freelancer Profiles & Portfolios](./16-freelancer-profiles.md)
