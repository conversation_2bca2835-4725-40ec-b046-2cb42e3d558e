import factory
from factory.django import DjangoModelFactory
from clients.models import Client


class ClientFactory(DjangoModelFactory):
    user = factory.SubFactory("registration.tests.factories.UserFactory")
    about = factory.Faker("text")
    website = factory.Faker("url")
    stripe_customer_id = "cus_test_customer_12345678"
    stripe_card_data = {"id": "card_123"}

    class Meta:
        model = Client
