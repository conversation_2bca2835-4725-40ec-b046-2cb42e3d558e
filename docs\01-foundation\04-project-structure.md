# Lesson 4: Project Structure & Organization 📁

## 🎯 Learning Objectives
By the end of this lesson, you will understand:
- How the Crelancer project is organized
- The purpose of each Django app
- Frontend asset organization
- Configuration and deployment structure
- File naming conventions and patterns

## 🏗️ High-Level Project Structure

```
crelancer/
├── 📁 backend/              # Django backend application
├── 📁 frontend/             # Frontend assets (JS, CSS, Webpack)
├── 📁 docs/                 # Documentation (this course!)
├── 📁 deploy/               # Deployment configurations
├── 📁 data/                 # Database data and backups
├── 📁 local_storage/        # Local file storage (development)
├── 📁 node_modules/         # NPM dependencies
├── 📄 docker-compose.yml    # Docker services configuration
├── 📄 Dockerfile            # Docker image definition
├── 📄 manage.py             # Django management script
├── 📄 requirements.txt      # Python dependencies
├── 📄 package.json          # Node.js dependencies
└── 📄 tailwind.config.js    # TailwindCSS configuration
```

## 🐍 Backend Structure (Django)

### **Main Backend Directory**
```
backend/
├── 📁 apps/                 # Django applications
├── 📁 core/                 # Shared utilities and base classes
├── 📁 settings/             # Environment-specific settings
├── 📁 templates/            # HTML templates
├── 📄 urls.py               # Main URL configuration
├── 📄 wsgi.py               # WSGI application entry point
└── 📄 asgi.py               # ASGI application entry point
```

### **Django Apps Organization**

#### **Core Apps (Business Logic)**

##### 1. **registration** 👤
```
Purpose: User authentication and account management
Features:
- User signup/signin
- Email verification
- Password reset
- Profile management
- Account deletion
```

##### 2. **talents** 👨‍💻
```
Purpose: Freelancer profiles and management
Features:
- Talent profile creation
- Skills and categories
- Verification system
- Portfolio integration
- Rate and availability settings
```

##### 3. **clients** 🏢
```
Purpose: Client profiles and company information
Features:
- Client profile management
- Company information
- Payment methods (Stripe)
- Hiring history
```

##### 4. **jobs** 💼
```
Purpose: Job posting and management
Features:
- Job creation and editing
- Category and skill tagging
- Job search and filtering
- Application management
- Job status tracking
```

##### 5. **deals** 🤝
```
Purpose: Contracts between clients and talents
Features:
- Deal creation and management
- Milestone-based payments
- Contract status tracking
- Hiring workflow
- Deal archiving
```

##### 6. **chat** 💬
```
Purpose: Communication system
Features:
- Real-time messaging
- File attachments
- Message history
- Unread message tracking
- Notification integration
```

##### 7. **finance** 💳
```
Purpose: Payment processing and financial transactions
Features:
- Stripe integration
- Payment intent creation
- Webhook handling
- Transaction tracking
- Fee calculation
```

#### **Supporting Apps**

##### 8. **portfolio** 🎨
```
Purpose: Talent portfolio management
Features:
- Project showcase
- Image uploads
- Category organization
- Portfolio display
```

##### 9. **reviews** ⭐
```
Purpose: Rating and review system
Features:
- Bidirectional reviews
- Rating calculations
- Review display
- Reputation tracking
```

##### 10. **disputes** ⚖️
```
Purpose: Conflict resolution
Features:
- Dispute creation
- Resolution workflow
- Admin intervention
- Documentation
```

##### 11. **notifications** 🔔
```
Purpose: User notifications
Features:
- Email notifications
- In-app notifications
- Notification preferences
- Delivery tracking
```

##### 12. **taxonomy** 🏷️
```
Purpose: Categorization and tagging
Features:
- Skills management
- Categories
- Countries and languages
- Hierarchical organization
```

### **App Structure Pattern**

Each Django app follows a consistent structure:

```
app_name/
├── 📁 migrations/           # Database migrations
├── 📁 tests/               # Unit tests
├── 📁 views/               # View modules (organized by functionality)
├── 📁 templates/           # App-specific templates
├── 📁 fixtures/            # Initial data
├── 📄 models.py            # Database models
├── 📄 urls.py              # URL patterns
├── 📄 forms.py             # Django forms
├── 📄 admin.py             # Admin interface
├── 📄 apps.py              # App configuration
├── 📄 managers.py          # Custom model managers
├── 📄 receivers.py         # Signal receivers
└── 📄 factories.py         # Test data factories
```

## 🎨 Frontend Structure

### **Frontend Directory Organization**
```
frontend/
├── 📁 src/                  # Source files
│   ├── 📁 application/      # Main application files
│   ├── 📁 controllers/      # Stimulus controllers
│   ├── 📁 stylesheets/      # CSS/SCSS files
│   └── 📄 application.js    # Main JavaScript entry point
├── 📁 build/                # Compiled assets (generated)
├── 📁 vendors/              # Third-party libraries
└── 📁 webpack/              # Webpack configuration
    ├── 📄 webpack.config.dev.js     # Development config
    ├── 📄 webpack.config.prod.js    # Production config
    └── 📄 webpack.config.watch.js   # Watch mode config
```

### **Asset Organization**

#### **JavaScript Structure**
```
frontend/src/
├── 📄 application.js        # Main entry point
├── 📁 controllers/          # Stimulus controllers
│   ├── 📄 chat_controller.js
│   ├── 📄 job_controller.js
│   ├── 📄 payment_controller.js
│   └── 📄 ...
└── 📁 application/
    ├── 📄 turbo.js          # Turbo configuration
    └── 📄 stimulus.js       # Stimulus setup
```

#### **Stylesheet Structure**
```
frontend/src/stylesheets/
├── 📄 application.scss      # Main stylesheet
├── 📁 components/           # Component styles
├── 📁 pages/               # Page-specific styles
└── 📁 utilities/           # Utility classes
```

## 🛠️ Core Infrastructure

### **Core Module**
```
backend/core/
├── 📄 models.py            # Base model classes
├── 📄 managers.py          # Custom model managers
├── 📄 validators.py        # Custom validators
├── 📄 storage_backends.py  # File storage backends
├── 📁 management/          # Custom Django commands
└── 📁 tests/               # Core functionality tests
```

**Key Base Classes:**
- `UnRemovableModel`: Soft delete functionality
- `TimestampedModel`: Created/updated timestamps
- `CustomManager`: Common query methods

### **Settings Organization**
```
backend/settings/
├── 📄 __init__.py          # Settings module
├── 📄 base.py              # Common settings
├── 📄 local.py             # Local development
├── 📄 production_local.py  # Production-like local
├── 📄 remote.py            # Production settings
├── 📄 test.py              # Testing settings
└── 📄 local_storage.py     # Local storage settings
```

## 📄 Templates Organization

### **Template Structure**
```
backend/templates/
├── 📄 base.html            # Base template
├── 📄 index.html           # Homepage
├── 📁 layouts/             # Layout templates
├── 📁 components/          # Reusable components
├── 📁 forms/               # Form templates
└── 📁 registration/        # Auth templates
```

### **Template Hierarchy**
```
base.html                   # Root template
├── layouts/app.html        # Main app layout
│   ├── talents/profile.html
│   ├── jobs/list.html
│   └── deals/detail.html
└── layouts/auth.html       # Authentication layout
    ├── registration/login.html
    └── registration/signup.html
```

## 🚀 Deployment Structure

### **Deployment Configurations**
```
deploy/
├── 📁 local/               # Local development
│   ├── 📄 .env.example
│   ├── 📄 docker-compose.yml
│   ├── 📄 Dockerfile
│   └── 📄 entrypoint.sh
├── 📁 remote/              # Production deployment
└── 📁 test/                # Testing environment
```

## 📊 URL Structure

### **Main URL Patterns**
```python
# backend/urls.py
urlpatterns = [
    path("", include("registration.urls")),           # Auth: /, /login, /signup
    path("crelancers/", include("talents.urls")),     # Talent profiles
    path("clients/", include("clients.urls")),        # Client profiles  
    path("jobs/", include("jobs.urls")),              # Job management
    path("portfolio/", include("portfolio.urls")),    # Portfolio
    path("deals/", include("deals.urls")),            # Contracts
    path("chat/", include("chat.urls")),              # Messaging
    path("finance/", include("finance.urls")),        # Payments
    path("disputes/", include("disputes.urls")),      # Disputes
    path("reviews/", include("reviews.urls")),        # Reviews
    path("admin/", admin.site.urls),                  # Admin panel
]
```

### **URL Naming Convention**
```python
# Example from deals/urls.py
app_name = "deals"
urlpatterns = [
    path("talent/deals/", TalentDealsListView.as_view(), name="talent_deals_list"),
    path("client/deals/", ClientDealsListView.as_view(), name="client_deals_list"),
    path("deals/<int:pk>/", DealDetailView.as_view(), name="deal_detail"),
]

# Usage in templates: {% url 'deals:talent_deals_list' %}
```

## 🗃️ Database Design Patterns

### **Model Relationships**
```python
# Example: User -> Profile relationship
class User(AbstractUser):
    role = models.CharField(choices=RoleChoices.choices)

class Talent(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    
class Client(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)

# Example: Many-to-Many with through model
class Deal(models.Model):
    job = models.ForeignKey(Job, on_delete=models.CASCADE)
    talent = models.ForeignKey(Talent, on_delete=models.CASCADE)
    client = models.ForeignKey(Client, on_delete=models.CASCADE)
```

## 📝 File Naming Conventions

### **Python Files**
- **Models**: `models.py` (singular)
- **Views**: `views.py` or `views/module_name.py`
- **URLs**: `urls.py`
- **Forms**: `forms.py`
- **Tests**: `test_*.py` or `tests/test_*.py`

### **Templates**
- **List views**: `model_list.html`
- **Detail views**: `model_detail.html`
- **Forms**: `model_form.html`
- **Partials**: `_partial_name.html`

### **Static Files**
- **Controllers**: `*_controller.js`
- **Stylesheets**: `*.scss`
- **Components**: `component-name.scss`

## 🔗 Inter-App Dependencies

### **Dependency Flow**
```
registration (User) 
    ↓
talents, clients (Profiles)
    ↓
jobs (Job Postings)
    ↓
deals (Contracts)
    ↓
chat, finance, reviews (Supporting Features)
```

### **Shared Dependencies**
- **taxonomy**: Used by jobs, talents for categorization
- **notifications**: Used by all apps for user notifications
- **core**: Provides base classes for all apps

## 🎯 Key Takeaways

1. **Modular Design**: Each app has a specific responsibility
2. **Consistent Structure**: All apps follow the same organization pattern
3. **Clear Separation**: Frontend and backend are clearly separated
4. **Environment-Specific**: Different configurations for different environments
5. **Scalable Architecture**: Easy to add new apps and features
6. **Django Best Practices**: Follows Django conventions and patterns

## 🔗 What's Next?

Now that you understand the project structure, let's dive deep into the Django backend architecture and see how the models, views, and templates work together.

**Next**: [Lesson 5: Django Architecture in Crelancer](../02-backend/05-django-architecture.md)

---

## 💡 Quick Quiz

1. Which app handles user authentication?
2. What's the purpose of the `core` module?
3. Where are Stimulus controllers located?
4. How are URL patterns organized?

*Answers: 1) registration, 2) Shared utilities and base classes, 3) frontend/src/controllers/, 4) Each app has its own urls.py with app_name namespace*
