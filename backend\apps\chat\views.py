import mimetypes
from django.shortcuts import render, reverse
from django.db import transaction
from django.shortcuts import redirect
from django.http import HttpResponse
from django.views import View
from django.views.generic import TemplateView, CreateView, ListView, DetailView
from django.utils.decorators import method_decorator
from django.core.exceptions import PermissionDenied
from django.contrib.auth.decorators import login_required
from turbo_response import TurboStream, TurboStreamResponse
from deals.models import Deal
from chat.models import Message, MessageAttachment, MessageNotification
from chat.forms import MessageCreateForm


@method_decorator(login_required(), name="dispatch")
class MainView(TemplateView):
    template_name = "chat/main.html"


class DealCheckMixin(View):
    """
    Adds deal object to context.
    Checks if user is a participant of deal.
    """

    deal = None

    def dispatch(self, request, *args, **kwargs):
        self.deal = Deal.objects.select_related("talent", "client").get(
            pk=self.kwargs["deal_id"]
        )
        if not self.deal.is_participant(request.user):
            raise PermissionDenied("User is not a participant of deal")

        self.set_deal_unread_to_zero()

        return super().dispatch(request, *args, **kwargs)

    def set_deal_unread_to_zero(self):
        if self.deal.talent.user == self.request.user:
            self.deal.talent_unread_count = 0
            MessageNotification.objects.filter(
                user=self.deal.talent.user, deal=self.deal
            ).delete()
        else:
            self.deal.client_unread_count = 0
            MessageNotification.objects.filter(
                user=self.deal.client.user, deal=self.deal
            ).delete()
        self.deal.save()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["deal"] = self.deal
        return context


@method_decorator(login_required(), name="dispatch")
class MessagesListView(DealCheckMixin, ListView):
    """
    List of messages for a deal.
    """

    model = Message
    template_name = "chat/message_list.html"
    context_object_name = (
        "chat_messages"  # messages conflicts with django.contrib.messages
    )
    paginate_by = 10

    def get_queryset(self):
        return self.model.objects.filter(deal_id=self.deal.id)


class DealsUnreadExistView(TemplateView):
    template_name = "deals/frames/unread_exist.html"

    def exists(self):
        exists = False
        if self.request.user.is_authenticated:
            if self.request.user.is_client:
                exists = Deal.objects.filter(
                    client=self.request.user.client, client_unread_count__gt=0
                ).exists()
            elif self.request.user.is_talent:
                exists = Deal.objects.filter(
                    talent=self.request.user.talent, talent_unread_count__gt=0
                ).exists()

        return exists

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["exists"] = self.exists()
        return context


@method_decorator(login_required(), name="dispatch")
class MessageCreateView(DealCheckMixin, CreateView):
    """
    Post a message to a deal's chat.
    """

    model = Message
    form_class = MessageCreateForm
    template_name = "chat/message_create.html"

    @transaction.atomic
    def form_valid(self, form):
        form.instance.deal = Deal.objects.get(pk=self.deal.id)
        form.instance.sender = self.request.user

        self.object = form.save()
        self.create_attachments(self.object, form.cleaned_data)

        # It is made to prevent sending message to stream before attachments are saved
        self.object.is_ready = True
        self.object.save()

        return redirect(self.get_success_url())

    def create_attachments(self, message, valid_data):
        files = self.request.FILES.getlist("attachments")
        # print("----attachments=", files)

        for file in files:
            # print("----file=", file)

            obj = MessageAttachment(
                message=message,
                file=file,
            )
            obj.full_clean()
            obj.save()

    def get_success_url(self):
        # Redirect to the empty form
        return reverse("chat:message_create", kwargs={"deal_id": self.deal.id})


@method_decorator(login_required(), name="dispatch")
class MessagesListStreamView(MessagesListView):
    """
    Returns a TurboStreamResponse with a list of messages and a loadmore button.
    """

    def render_to_response(self, context, **response_kwargs):
        return TurboStreamResponse(
            [
                # add message to div.id="my-deal-messages"
                TurboStream("my-deal-messages")
                .prepend.template("chat/components/message_list.html", context)
                .render(),
                # replace a loadmore button with a new one
                TurboStream("loadmore")
                .update.template("chat/components/loadmore.html", context)
                .render(),
            ]
        )


@method_decorator(login_required(), name="dispatch")
class MessageAttachmentView(DetailView):
    """
    Download a message attachment.
    """

    model = MessageAttachment

    def get(self, request, *args, **kwargs):
        attachment = self.get_object()

        if not attachment.message.deal.is_participant(request.user):
            raise PermissionDenied()

        return self.download_response(attachment)

    @staticmethod
    def download_response(attachment):
        mimetype = mimetypes.guess_type(attachment.file.name)[0]

        response = HttpResponse(attachment.file, content_type=mimetype)
        response["Content-Disposition"] = f"attachment; filename={attachment.filename}"
        return response
