# Lesson 27: Production Deployment 🚀

## 🎯 Learning Objectives
- Production environment setup
- Cloud deployment strategies (AWS, DigitalOcean)
- CI/CD pipeline implementation
- Security best practices
- Monitoring and maintenance

## ☁️ Cloud Infrastructure

### **AWS Deployment Architecture**
```yaml
# infrastructure/aws/docker-compose.aws.yml
version: '3.8'

services:
  web:
    image: ${ECR_REGISTRY}/crelancer-web:${IMAGE_TAG}
    environment:
      - DATABASE_URL=${RDS_DATABASE_URL}
      - REDIS_URL=${ELASTICACHE_URL}
      - AWS_STORAGE_BUCKET_NAME=${S3_BUCKET}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - SECRET_KEY=${SECRET_KEY}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS}
    ports:
      - "8000:8000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: awslogs
      options:
        awslogs-group: /ecs/crelancer
        awslogs-region: us-east-1
        awslogs-stream-prefix: web

  celery:
    image: ${ECR_REGISTRY}/crelancer-web:${IMAGE_TAG}
    environment:
      - DATABASE_URL=${RDS_DATABASE_URL}
      - REDIS_URL=${ELASTICACHE_URL}
      - CELERY_BROKER_URL=${ELASTICACHE_URL}
    command: celery -A backend worker -l info --concurrency=4
    logging:
      driver: awslogs
      options:
        awslogs-group: /ecs/crelancer
        awslogs-region: us-east-1
        awslogs-stream-prefix: celery
```

### **Terraform Infrastructure**
```hcl
# infrastructure/terraform/main.tf
provider "aws" {
  region = var.aws_region
}

# VPC and Networking
resource "aws_vpc" "main" {
  cidr_block           = "10.0.0.0/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name = "crelancer-vpc"
  }
}

resource "aws_subnet" "public" {
  count             = 2
  vpc_id            = aws_vpc.main.id
  cidr_block        = "10.0.${count.index + 1}.0/24"
  availability_zone = data.aws_availability_zones.available.names[count.index]

  map_public_ip_on_launch = true

  tags = {
    Name = "crelancer-public-${count.index + 1}"
  }
}

resource "aws_subnet" "private" {
  count             = 2
  vpc_id            = aws_vpc.main.id
  cidr_block        = "10.0.${count.index + 10}.0/24"
  availability_zone = data.aws_availability_zones.available.names[count.index]

  tags = {
    Name = "crelancer-private-${count.index + 1}"
  }
}

# RDS Database
resource "aws_db_instance" "postgres" {
  identifier = "crelancer-db"
  
  engine         = "postgres"
  engine_version = "15.3"
  instance_class = "db.t3.micro"
  
  allocated_storage     = 20
  max_allocated_storage = 100
  storage_type          = "gp2"
  storage_encrypted     = true
  
  db_name  = "crelancer"
  username = var.db_username
  password = var.db_password
  
  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.main.name
  
  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  skip_final_snapshot = false
  final_snapshot_identifier = "crelancer-final-snapshot"
  
  tags = {
    Name = "crelancer-database"
  }
}

# ElastiCache Redis
resource "aws_elasticache_subnet_group" "main" {
  name       = "crelancer-cache-subnet"
  subnet_ids = aws_subnet.private[*].id
}

resource "aws_elasticache_cluster" "redis" {
  cluster_id           = "crelancer-redis"
  engine               = "redis"
  node_type            = "cache.t3.micro"
  num_cache_nodes      = 1
  parameter_group_name = "default.redis7"
  port                 = 6379
  subnet_group_name    = aws_elasticache_subnet_group.main.name
  security_group_ids   = [aws_security_group.redis.id]
}

# S3 Bucket for media files
resource "aws_s3_bucket" "media" {
  bucket = "crelancer-media-${random_string.bucket_suffix.result}"
}

resource "aws_s3_bucket_versioning" "media" {
  bucket = aws_s3_bucket.media.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "media" {
  bucket = aws_s3_bucket.media.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# ECS Cluster
resource "aws_ecs_cluster" "main" {
  name = "crelancer"

  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}

# Application Load Balancer
resource "aws_lb" "main" {
  name               = "crelancer-alb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb.id]
  subnets            = aws_subnet.public[*].id

  enable_deletion_protection = false

  tags = {
    Name = "crelancer-alb"
  }
}

# ECS Service
resource "aws_ecs_service" "web" {
  name            = "crelancer-web"
  cluster         = aws_ecs_cluster.main.id
  task_definition = aws_ecs_task_definition.web.arn
  desired_count   = 2

  load_balancer {
    target_group_arn = aws_lb_target_group.web.arn
    container_name   = "web"
    container_port   = 8000
  }

  network_configuration {
    security_groups = [aws_security_group.ecs_tasks.id]
    subnets         = aws_subnet.private[*].id
  }

  depends_on = [aws_lb_listener.web]
}
```

## 🔄 CI/CD Pipeline

### **GitHub Actions Workflow**
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  AWS_REGION: us-east-1
  ECR_REPOSITORY: crelancer

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements/test.txt

    - name: Run tests
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        REDIS_URL: redis://localhost:6379/0
      run: |
        python backend/manage.py test
        
    - name: Run linting
      run: |
        flake8 backend/
        black --check backend/
        
    - name: Security scan
      run: |
        bandit -r backend/
        safety check

  build-and-deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build frontend
      run: |
        cd frontend
        npm ci
        npm run build

    - name: Build, tag, and push image to Amazon ECR
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: ${{ github.sha }}
      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        docker tag $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:latest
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest

    - name: Deploy to ECS
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: ${{ github.sha }}
      run: |
        # Update ECS service
        aws ecs update-service \
          --cluster crelancer \
          --service crelancer-web \
          --force-new-deployment

    - name: Run database migrations
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: ${{ github.sha }}
      run: |
        # Run migrations using ECS task
        aws ecs run-task \
          --cluster crelancer \
          --task-definition crelancer-migrate \
          --launch-type FARGATE \
          --network-configuration "awsvpcConfiguration={subnets=[subnet-xxx],securityGroups=[sg-xxx],assignPublicIp=ENABLED}"

    - name: Notify deployment
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

### **Deployment Scripts**
```bash
#!/bin/bash
# scripts/deploy-production.sh

set -e

echo "🚀 Starting production deployment..."

# Configuration
ENVIRONMENT="production"
AWS_REGION="us-east-1"
ECR_REPOSITORY="crelancer"
ECS_CLUSTER="crelancer"
ECS_SERVICE="crelancer-web"

# Get current commit hash
COMMIT_HASH=$(git rev-parse HEAD)
IMAGE_TAG=${COMMIT_HASH:0:8}

echo "📦 Building and pushing Docker image..."

# Login to ECR
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REGISTRY

# Build and push image
docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

# Tag as latest
docker tag $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:latest
docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest

echo "🗄️ Running database migrations..."

# Create migration task definition
cat > migrate-task.json << EOF
{
  "family": "crelancer-migrate",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "migrate",
      "image": "$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG",
      "command": ["python", "backend/manage.py", "migrate"],
      "environment": [
        {"name": "DATABASE_URL", "value": "$DATABASE_URL"},
        {"name": "SECRET_KEY", "value": "$SECRET_KEY"}
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/crelancer",
          "awslogs-region": "$AWS_REGION",
          "awslogs-stream-prefix": "migrate"
        }
      }
    }
  ]
}
EOF

# Register and run migration task
aws ecs register-task-definition --cli-input-json file://migrate-task.json
aws ecs run-task \
  --cluster $ECS_CLUSTER \
  --task-definition crelancer-migrate \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[$SUBNET_IDS],securityGroups=[$SECURITY_GROUP_ID],assignPublicIp=ENABLED}"

echo "🔄 Updating ECS service..."

# Update service with new image
aws ecs update-service \
  --cluster $ECS_CLUSTER \
  --service $ECS_SERVICE \
  --force-new-deployment

# Wait for deployment to complete
echo "⏳ Waiting for deployment to complete..."
aws ecs wait services-stable \
  --cluster $ECS_CLUSTER \
  --services $ECS_SERVICE

echo "🔍 Running health checks..."

# Health check
LOAD_BALANCER_DNS=$(aws elbv2 describe-load-balancers \
  --names crelancer-alb \
  --query 'LoadBalancers[0].DNSName' \
  --output text)

for i in {1..30}; do
  if curl -f "http://$LOAD_BALANCER_DNS/health/"; then
    echo "✅ Health check passed!"
    break
  fi
  
  if [ $i -eq 30 ]; then
    echo "❌ Health check failed after 30 attempts"
    exit 1
  fi
  
  echo "⏳ Health check attempt $i/30..."
  sleep 10
done

echo "🎉 Production deployment completed successfully!"

# Clean up
rm -f migrate-task.json
```

## 🔒 Security Configuration

### **Environment Variables Management**
```bash
# scripts/setup-secrets.sh
#!/bin/bash

set -e

echo "🔐 Setting up production secrets..."

# Create secrets in AWS Secrets Manager
aws secretsmanager create-secret \
  --name "crelancer/database" \
  --description "Database credentials for Crelancer" \
  --secret-string '{
    "username": "'$DB_USERNAME'",
    "password": "'$DB_PASSWORD'",
    "host": "'$DB_HOST'",
    "port": 5432,
    "dbname": "crelancer"
  }'

aws secretsmanager create-secret \
  --name "crelancer/django" \
  --description "Django configuration for Crelancer" \
  --secret-string '{
    "SECRET_KEY": "'$SECRET_KEY'",
    "STRIPE_SECRET_KEY": "'$STRIPE_SECRET_KEY'",
    "AWS_ACCESS_KEY_ID": "'$AWS_ACCESS_KEY_ID'",
    "AWS_SECRET_ACCESS_KEY": "'$AWS_SECRET_ACCESS_KEY'"
  }'

echo "✅ Secrets created successfully!"
```

### **Security Groups Configuration**
```hcl
# infrastructure/terraform/security.tf

# ALB Security Group
resource "aws_security_group" "alb" {
  name_prefix = "crelancer-alb-"
  vpc_id      = aws_vpc.main.id

  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "crelancer-alb-sg"
  }
}

# ECS Tasks Security Group
resource "aws_security_group" "ecs_tasks" {
  name_prefix = "crelancer-ecs-tasks-"
  vpc_id      = aws_vpc.main.id

  ingress {
    from_port       = 8000
    to_port         = 8000
    protocol        = "tcp"
    security_groups = [aws_security_group.alb.id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "crelancer-ecs-tasks-sg"
  }
}

# RDS Security Group
resource "aws_security_group" "rds" {
  name_prefix = "crelancer-rds-"
  vpc_id      = aws_vpc.main.id

  ingress {
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [aws_security_group.ecs_tasks.id]
  }

  tags = {
    Name = "crelancer-rds-sg"
  }
}
```

## 📊 Monitoring and Alerting

### **CloudWatch Alarms**
```hcl
# infrastructure/terraform/monitoring.tf

resource "aws_cloudwatch_metric_alarm" "high_cpu" {
  alarm_name          = "crelancer-high-cpu"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/ECS"
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "This metric monitors ECS CPU utilization"
  alarm_actions       = [aws_sns_topic.alerts.arn]

  dimensions = {
    ServiceName = aws_ecs_service.web.name
    ClusterName = aws_ecs_cluster.main.name
  }
}

resource "aws_cloudwatch_metric_alarm" "high_memory" {
  alarm_name          = "crelancer-high-memory"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "MemoryUtilization"
  namespace           = "AWS/ECS"
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "This metric monitors ECS memory utilization"
  alarm_actions       = [aws_sns_topic.alerts.arn]

  dimensions = {
    ServiceName = aws_ecs_service.web.name
    ClusterName = aws_ecs_cluster.main.name
  }
}

resource "aws_cloudwatch_metric_alarm" "database_cpu" {
  alarm_name          = "crelancer-db-high-cpu"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/RDS"
  period              = "300"
  statistic           = "Average"
  threshold           = "75"
  alarm_description   = "This metric monitors RDS CPU utilization"
  alarm_actions       = [aws_sns_topic.alerts.arn]

  dimensions = {
    DBInstanceIdentifier = aws_db_instance.postgres.id
  }
}
```

### **Application Monitoring**
```python
# backend/core/monitoring.py
import logging
import boto3
from django.conf import settings

logger = logging.getLogger(__name__)

class CloudWatchMetrics:
    def __init__(self):
        self.cloudwatch = boto3.client('cloudwatch', region_name=settings.AWS_REGION)
    
    def put_metric(self, metric_name, value, unit='Count', namespace='Crelancer'):
        """Send custom metric to CloudWatch"""
        try:
            self.cloudwatch.put_metric_data(
                Namespace=namespace,
                MetricData=[
                    {
                        'MetricName': metric_name,
                        'Value': value,
                        'Unit': unit,
                        'Timestamp': datetime.utcnow()
                    }
                ]
            )
        except Exception as e:
            logger.error(f"Failed to send metric {metric_name}: {e}")
    
    def track_user_registration(self, user_type):
        """Track user registrations"""
        self.put_metric(f'UserRegistration_{user_type}', 1)
    
    def track_job_posting(self):
        """Track job postings"""
        self.put_metric('JobPosted', 1)
    
    def track_deal_completion(self, amount):
        """Track deal completions"""
        self.put_metric('DealCompleted', 1)
        self.put_metric('DealValue', float(amount), 'None')

# Usage in views
metrics = CloudWatchMetrics()

class JobCreateView(CreateView):
    def form_valid(self, form):
        response = super().form_valid(form)
        metrics.track_job_posting()
        return response
```

## 🎯 Key Takeaways

1. **Infrastructure as Code**: Use Terraform for reproducible infrastructure
2. **Automated Deployment**: CI/CD pipeline with testing and security checks
3. **Security**: Proper secrets management and network security
4. **Monitoring**: Comprehensive monitoring and alerting setup
5. **Scalability**: Auto-scaling and load balancing configuration

## 🔗 What's Next?

**Next**: [Lesson 28: Monitoring & Maintenance](./28-monitoring.md)
