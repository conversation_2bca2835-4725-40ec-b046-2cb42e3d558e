# Lesson 6: Models & Database Design 🗄️

## 🎯 Learning Objectives
By the end of this lesson, you will understand:
- Complete database schema and relationships
- Business logic implementation in models
- Data validation and constraints
- Performance optimization strategies
- Model methods and properties

## 🏗️ Database Schema Overview

### **Core Entity Relationship Diagram**
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    User     │    │   Talent    │    │   Client    │
│             │◄──►│             │    │             │
│ - email     │    │ - skills    │    │ - about     │
│ - role      │    │ - rate      │    │ - website   │
│ - name      │    │ - verified  │    │ - stripe_id │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    Job      │◄──►│    Deal     │◄──►│   Message   │
│             │    │             │    │             │
│ - title     │    │ - status    │    │ - content   │
│ - budget    │    │ - proposal  │    │ - timestamp │
│ - skills    │    │ - milestones│    │ - sender    │
└─────────────┘    └─────────────┘    └─────────────┘
```

## 👤 User and Profile Models

### **Base User Model**
```python
class User(AbstractUser, UnRemovableModel):
    """
    Custom user model with email authentication and role-based access
    """
    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = ["first_name", "last_name"]
    
    class RoleChoices(models.TextChoices):
        TALENT = "talent", "Talent"
        CLIENT = "client", "Client"
    
    # Remove username, use email instead
    username = None
    email = LowercaseEmailField(_("email address"), unique=True)
    role = models.CharField(max_length=10, choices=RoleChoices.choices)
    
    # Profile completion tracking
    is_email_confirmed = models.BooleanField(default=False)
    date_joined = models.DateTimeField(auto_now_add=True)
    
    # Soft delete from UnRemovableModel
    is_removed = models.BooleanField(default=False)
    
    def get_profile(self):
        """Get the appropriate profile based on user role"""
        if self.role == self.RoleChoices.TALENT:
            return getattr(self, 'talent', None)
        elif self.role == self.RoleChoices.CLIENT:
            return getattr(self, 'client', None)
        return None
    
    def get_full_name(self):
        return f"{self.first_name} {self.last_name}".strip()
```

### **Talent Profile Model**
```python
class Talent(models.Model):
    """
    Freelancer profile with skills, verification, and payment info
    """
    user = models.OneToOneField("registration.User", on_delete=models.CASCADE)
    
    # Profile status
    is_verified = models.BooleanField(default=False)
    is_published = models.BooleanField(default=False)
    
    # Professional information
    professional_title = models.CharField(max_length=255, null=True)
    about = models.TextField(null=True, blank=True)
    
    # Skills and categories (Many-to-Many relationships)
    skills = SortedManyToManyField("taxonomy.Skill", through="talents.TalentSkill")
    categories = models.ManyToManyField("taxonomy.Category")
    
    # Work preferences
    rate_hourly = models.DecimalField(
        max_digits=10, decimal_places=2, null=True,
        validators=[MinValueValidator(1)]
    )
    hours_weekly = models.PositiveIntegerField(
        null=True, validators=[MinValueValidator(1), MaxValueValidator(168)]
    )
    
    # Payment integration
    stripe_account_id = models.CharField(max_length=255, null=True)
    stripe_account_data = models.JSONField(null=True)
    
    # Denormalized fields for performance
    reviews_rating = models.DecimalField(max_digits=3, decimal_places=2, default=0)
    reviews_count = models.PositiveIntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    objects = TalentManager()
    
    def is_profile_complete(self):
        """Check if profile has minimum required information"""
        return all([
            self.professional_title,
            self.about,
            self.rate_hourly,
            self.skills.exists(),
            self.categories.exists()
        ])
    
    def can_apply_for_jobs(self):
        """Check if talent can apply for jobs"""
        return self.is_verified and self.is_published and self.is_profile_complete()
```

### **Client Profile Model**
```python
class Client(models.Model):
    """
    Client profile with company information and payment methods
    """
    user = models.OneToOneField("registration.User", on_delete=models.CASCADE)
    
    # Company information
    about = models.TextField(null=True, blank=True)
    website = models.URLField(null=True, blank=True)
    
    # Payment integration
    stripe_customer_id = models.CharField(max_length=255, null=True)
    stripe_card_data = models.JSONField(null=True)
    
    # Denormalized fields for performance
    reviews_rating = models.DecimalField(max_digits=3, decimal_places=2, default=0)
    reviews_count = models.PositiveIntegerField(default=0)
    
    objects = ClientManager()
    
    def has_payment_method(self):
        """Check if client has a payment method on file"""
        return bool(self.stripe_customer_id and self.stripe_card_data)
```

## 💼 Job and Deal Models

### **Job Model**
```python
class Job(models.Model):
    """
    Job posting with requirements, budget, and application management
    """
    class StatusChoices(models.TextChoices):
        DRAFT = "draft", "Draft"
        PUBLISHED = "published", "Published"
        IN_PROGRESS = "in_progress", "In Progress"
        COMPLETED = "completed", "Completed"
        CANCELLED = "cancelled", "Cancelled"
    
    # Ownership
    client = models.ForeignKey("clients.Client", on_delete=models.CASCADE)
    
    # Job details
    title = models.CharField(max_length=255)
    description = models.TextField()
    
    # Categorization
    category = models.ForeignKey("taxonomy.Category", on_delete=models.CASCADE)
    skills = models.ManyToManyField("taxonomy.Skill", blank=True)
    
    # Budget and timeline
    budget_min = models.DecimalField(max_digits=10, decimal_places=2)
    budget_max = models.DecimalField(max_digits=10, decimal_places=2)
    duration_weeks = models.PositiveIntegerField(null=True, blank=True)
    
    # Status and timestamps
    status = models.CharField(max_length=20, choices=StatusChoices.choices, default=StatusChoices.DRAFT)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    published_at = models.DateTimeField(null=True, blank=True)
    
    # Denormalized fields
    applications_count = models.PositiveIntegerField(default=0)
    
    objects = JobQuerySet.as_manager()
    
    class Meta:
        indexes = [
            models.Index(fields=['status', 'published_at']),
            models.Index(fields=['category', 'status']),
            models.Index(fields=['client', 'status']),
        ]
        ordering = ['-published_at', '-created_at']
    
    def publish(self):
        """Publish the job and set published timestamp"""
        self.status = self.StatusChoices.PUBLISHED
        self.published_at = timezone.now()
        self.save()
    
    def is_accepting_applications(self):
        """Check if job is accepting new applications"""
        return self.status == self.StatusChoices.PUBLISHED
    
    def get_budget_display(self):
        """Format budget range for display"""
        if self.budget_min == self.budget_max:
            return f"${self.budget_min:,.0f}"
        return f"${self.budget_min:,.0f} - ${self.budget_max:,.0f}"
```

### **Deal Model (Contract)**
```python
class Deal(models.Model):
    """
    Contract between client and talent for a specific job
    """
    class StatusChoices(models.TextChoices):
        PENDING = "pending", "Pending"
        ACTIVE = "active", "Active"
        COMPLETED = "completed", "Completed"
        CANCELLED = "cancelled", "Cancelled"
        DISPUTED = "disputed", "Disputed"
    
    # Core relationship
    job = models.ForeignKey("jobs.Job", on_delete=models.CASCADE)
    talent = models.ForeignKey("talents.Talent", on_delete=models.CASCADE)
    client = models.ForeignKey("clients.Client", on_delete=models.CASCADE)
    
    # Deal details
    status = models.CharField(max_length=20, choices=StatusChoices.choices, default=StatusChoices.PENDING)
    proposal = models.TextField()
    agreed_rate = models.DecimalField(max_digits=10, decimal_places=2, null=True)
    
    # Chat functionality
    last_message_at = models.DateTimeField(auto_now_add=True)
    client_unread_count = models.PositiveIntegerField(default=0)
    talent_unread_count = models.PositiveIntegerField(default=0)
    
    # User settings
    user_deal_settings = models.ManyToManyField("registration.User", through="deals.UserDealSettings")
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    hired_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    objects = DealsManager()
    
    class Meta:
        unique_together = [["job", "talent"]]  # One application per job-talent pair
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['last_message_at']),
            models.Index(fields=['client', 'status']),
            models.Index(fields=['talent', 'status']),
        ]
        ordering = ['-last_message_at']
    
    def save(self, *args, **kwargs):
        # Auto-set client from job if not provided
        if not self.client_id:
            self.client = self.job.client
        super().save(*args, **kwargs)
    
    def hire(self):
        """Activate the deal (hire the talent)"""
        self.status = self.StatusChoices.ACTIVE
        self.hired_at = timezone.now()
        self.save()
        
        # Update job status
        self.job.status = Job.StatusChoices.IN_PROGRESS
        self.job.save()
    
    def complete(self):
        """Mark deal as completed"""
        self.status = self.StatusChoices.COMPLETED
        self.completed_at = timezone.now()
        self.save()
```

## 💬 Communication Models

### **Message Model**
```python
class Message(models.Model):
    """
    Chat messages between client and talent within a deal
    """
    class MessageType(models.TextChoices):
        TEXT = "text", "Text"
        SYSTEM = "system", "System"
        FILE = "file", "File"
    
    deal = models.ForeignKey("deals.Deal", on_delete=models.CASCADE, related_name="messages")
    sender = models.ForeignKey("registration.User", on_delete=models.CASCADE)
    
    # Message content
    message_type = models.CharField(max_length=10, choices=MessageType.choices, default=MessageType.TEXT)
    content = models.TextField()
    
    # File attachment
    attachment = models.FileField(upload_to="chat/attachments/%Y/%m/", null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    
    # Read status
    is_read_by_client = models.BooleanField(default=False)
    is_read_by_talent = models.BooleanField(default=False)
    
    class Meta:
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['deal', 'created_at']),
            models.Index(fields=['sender', 'created_at']),
        ]
    
    def save(self, *args, **kwargs):
        is_new = self.pk is None
        super().save(*args, **kwargs)
        
        if is_new:
            # Update deal's last message timestamp
            self.deal.last_message_at = self.created_at
            
            # Update unread counts
            if self.sender.role == User.RoleChoices.CLIENT:
                self.deal.talent_unread_count += 1
            else:
                self.deal.client_unread_count += 1
            
            self.deal.save()
```

## 💳 Finance Models

### **Transaction Model**
```python
class Transaction(models.Model):
    """
    Financial transactions for payments and payouts
    """
    class TransactionType(models.TextChoices):
        PAYMENT = "payment", "Payment"
        PAYOUT = "payout", "Payout"
        REFUND = "refund", "Refund"
        FEE = "fee", "Platform Fee"
    
    class Status(models.TextChoices):
        PENDING = "pending", "Pending"
        COMPLETED = "completed", "Completed"
        FAILED = "failed", "Failed"
        CANCELLED = "cancelled", "Cancelled"
    
    # Related entities
    deal = models.ForeignKey("deals.Deal", on_delete=models.CASCADE)
    milestone = models.ForeignKey("jobs.Milestone", on_delete=models.CASCADE, null=True)
    
    # Transaction details
    transaction_type = models.CharField(max_length=20, choices=TransactionType.choices)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    fee_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    status = models.CharField(max_length=20, choices=Status.choices, default=Status.PENDING)
    
    # Stripe integration
    stripe_payment_intent_id = models.CharField(max_length=255, null=True)
    stripe_transfer_id = models.CharField(max_length=255, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['deal', 'status']),
            models.Index(fields=['transaction_type', 'status']),
        ]
```

## 🏷️ Taxonomy Models

### **Category and Skill Models**
```python
class Category(models.Model):
    """
    Job and talent categories (e.g., Web Development, Design)
    """
    name = models.CharField(max_length=255, unique=True)
    slug = models.SlugField(unique=True)
    description = models.TextField(blank=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True)
    
    # Display settings
    icon = models.CharField(max_length=50, blank=True)
    color = models.CharField(max_length=7, default="#6B7280")  # Hex color
    
    # Ordering
    sort_order = models.PositiveIntegerField(default=0)
    
    class Meta:
        verbose_name_plural = "categories"
        ordering = ['sort_order', 'name']
    
    def __str__(self):
        return self.name

class Skill(models.Model):
    """
    Skills for jobs and talents (e.g., Python, React, Photoshop)
    """
    name = models.CharField(max_length=255, unique=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE, null=True)
    
    # Usage statistics
    usage_count = models.PositiveIntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = CurrentUserField()
    
    class Meta:
        ordering = ['-usage_count', 'name']
    
    def __str__(self):
        return self.name
```

## 📊 Performance Optimization

### **Database Indexes**
```python
# Strategic indexes for common queries
class Meta:
    indexes = [
        # Job search indexes
        models.Index(fields=['status', 'published_at']),
        models.Index(fields=['category', 'status']),
        models.Index(fields=['budget_min', 'budget_max']),
        
        # Deal management indexes
        models.Index(fields=['client', 'status']),
        models.Index(fields=['talent', 'status']),
        models.Index(fields=['last_message_at']),
        
        # Chat indexes
        models.Index(fields=['deal', 'created_at']),
        models.Index(fields=['sender', 'created_at']),
    ]
```

### **Denormalized Fields**
```python
# Performance optimization through denormalization
class Talent(models.Model):
    # Calculated from reviews
    reviews_rating = models.DecimalField(max_digits=3, decimal_places=2, default=0)
    reviews_count = models.PositiveIntegerField(default=0)

class Job(models.Model):
    # Calculated from deals
    applications_count = models.PositiveIntegerField(default=0)

class Deal(models.Model):
    # Chat optimization
    client_unread_count = models.PositiveIntegerField(default=0)
    talent_unread_count = models.PositiveIntegerField(default=0)
```

### **Custom Managers for Common Queries**
```python
class TalentManager(models.Manager):
    def available(self):
        return self.filter(is_published=True, is_verified=True)
    
    def with_skills(self, skills):
        return self.filter(skills__in=skills).distinct()
    
    def in_categories(self, categories):
        return self.filter(categories__in=categories).distinct()

class JobQuerySet(models.QuerySet):
    def published(self):
        return self.filter(status=Job.StatusChoices.PUBLISHED)
    
    def in_budget_range(self, min_budget, max_budget):
        return self.filter(
            budget_min__lte=max_budget,
            budget_max__gte=min_budget
        )
```

## 🔗 Model Relationships Summary

### **One-to-One Relationships**
- User ↔ Talent (profile)
- User ↔ Client (profile)

### **One-to-Many Relationships**
- Client → Jobs (ownership)
- Job → Deals (applications)
- Deal → Messages (chat)
- Deal → Transactions (payments)

### **Many-to-Many Relationships**
- Job ↔ Skills (requirements)
- Talent ↔ Skills (capabilities)
- Talent ↔ Categories (specializations)

## 🎯 Key Takeaways

1. **Email-based Authentication**: Custom user model with role-based profiles
2. **Flexible Relationships**: Clear separation between users, profiles, and business entities
3. **Performance Optimization**: Strategic indexes and denormalized fields
4. **Business Logic in Models**: Methods for common operations and validations
5. **Audit Trail**: Timestamps and status tracking throughout the system
6. **Stripe Integration**: Payment processing built into the model layer

## 🔗 What's Next?

Now that you understand the data layer, let's explore how views and URL patterns handle the business logic and user interactions.

**Next**: [Lesson 7: Views & URL Patterns](./07-views-urls.md)

---

## 💡 Quick Quiz

1. What field is used as the USERNAME_FIELD in the custom User model?
2. How is the relationship between Job and Talent represented?
3. What's the purpose of denormalized fields like reviews_rating?
4. How are unread message counts tracked?

*Answers: 1) email, 2) Through the Deal model (many-to-many with extra fields), 3) Performance optimization to avoid complex queries, 4) Incremented on message save and stored in Deal model*
