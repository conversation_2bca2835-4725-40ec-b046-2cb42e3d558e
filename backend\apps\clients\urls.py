from django.urls import path, include
from django.views.generic import TemplateView
from .views import ClientProfileView, ClientCreateView, BasicShowView, BasicChangeView

app_name = "clients"

urlpatterns = [
    path(
        "not-a-client/",
        TemplateView.as_view(template_name="clients/not_a_client.html"),
        name="not_a_client",
    ),
    # path("create/", ClientCreateView.as_view(), name="create"),
    path(
        "profile/",
        include(
            [
                path("", ClientProfileView.as_view(), name="profile"),
                path(
                    "frames/",
                    include(
                        [
                            path(
                                "basic/",
                                include(
                                    [
                                        path(
                                            "",
                                            BasicShowView.as_view(),
                                            name="basic_show",
                                        ),
                                        path(
                                            "edit/",
                                            BasicChangeView.as_view(),
                                            name="basic_change",
                                        ),
                                    ]
                                ),
                            ),
                        ],
                    ),
                ),
            ]
        ),
    ),
]
