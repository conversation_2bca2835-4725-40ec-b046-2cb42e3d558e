from django.shortcuts import redirect
from functools import wraps
from django.core.exceptions import PermissionDenied


def logined_client_required(redirect_url=None):
    """
    Check if user is a client. If not, redirect to redirect_url.
    Also check if user is authenticated. If not, redirect to login page.
    """

    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if not request.user.is_authenticated:
                return redirect("login")

            if not request.user.is_client:
                if redirect_url is not None:
                    return redirect(redirect_url)
                else:
                    raise PermissionDenied("Client profile required")

            return view_func(request, *args, **kwargs)

        return _wrapped_view

    return decorator
