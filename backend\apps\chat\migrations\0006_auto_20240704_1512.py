# Generated by Django 3.2.18 on 2024-07-04 15:12

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('deals', '0005_alter_deal_status'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('chat', '0005_message_is_ready'),
    ]

    operations = [
        migrations.CreateModel(
            name='MessageNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('last_message_at', models.DateTimeField(auto_now_add=True)),
                ('deal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='deals.deal')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-last_message_at'],
                'default_permissions': (),
            },
        ),
        migrations.AddIndex(
            model_name='messagenotification',
            index=models.Index(fields=['last_message_at'], name='chat_messag_last_me_c5f604_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='messagenotification',
            unique_together={('deal', 'user')},
        ),
    ]
