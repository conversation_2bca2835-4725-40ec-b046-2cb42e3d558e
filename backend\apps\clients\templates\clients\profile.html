{% extends 'layouts/profile.html' %}

{% block title %}Client profile{% endblock %}


{% block aside %}
    {% include 'clients/components/aside.html' %}
{% endblock %}


{% block main %}
    <div class="md:border border-gray-300 px-12 md:px-8 py-6 rounded-t-lg">
        <turbo-frame id="profile_personal" src="{% url "personal_client_show" %}">
            {% include 'components/preloader.html'%}
        </turbo-frame>
    </div>

    <div class="md:border-x md:border-b border-gray-300 px-12 md:px-8 py-6 rounded-b-lg">
        <turbo-frame id="profile_basic" src="{% url "clients:basic_show" %}">
            {% include 'components/preloader.html'%}
        </turbo-frame>
    </div>

    {% if user.client.has_payment_method %}
    <div class="px-12 md:px-0 md:pt-8">
        <a
                href="{% url 'jobs:create_basic' %}" type="submit"
                class="w-full md:w-44 px-5 py-2.5 sm:py-3.5 text-sm font-medium text-center text-white rounded-[100px] border border-downy-300 bg-downy-300 hover:bg-purple-800 hover:border-purple-800 focus:ring-0">
            Post a new job
        </a>
    </div>
    {% endif %}

{% endblock %}
