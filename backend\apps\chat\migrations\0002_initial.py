# Generated by Django 3.2.18 on 2023-08-21 11:21

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('chat', '0001_initial'),
        ('deals', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='message',
            name='deal',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='deals.deal'),
        ),
        migrations.AddField(
            model_name='message',
            name='sender',
            field=models.ForeignKey(help_text='If Null, then system message', null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='message',
            index=models.Index(fields=['created_at'], name='chat_messag_created_b6b51c_idx'),
        ),
    ]
