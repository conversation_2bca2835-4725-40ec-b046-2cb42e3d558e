import os
from django.db import models
from django.urls import reverse
from django.conf import settings
from django.utils.module_loading import import_string
from backend.core.validators import FileValidator


class Message(models.Model):
    deal = models.ForeignKey(
        "deals.Deal",
        on_delete=models.CASCADE,
        related_name="messages",
    )
    sender = models.ForeignKey(
        "registration.User",
        on_delete=models.CASCADE,
        null=True,
        help_text="If Null, then system message",
    )
    text = models.TextField(null=True)
    # we are waiting for related objects to be saved. No attachments will be shown without it
    is_ready = models.BooleanField(
        default=False, help_text="Is message ready to be sent to stream"
    )
    template = models.CharField(null=True, max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=["created_at"]),
        ]
        ordering = ["-created_at"]
        default_permissions = ()


class MessageAttachment(models.Model):
    message = models.ForeignKey(
        "chat.Message",
        on_delete=models.CASCADE,
        related_name="attachments",
    )
    file = models.FileField(
        upload_to="messages/attachments/%Y-%m/",
        storage=import_string(settings.PRIVATE_MEDIA_STORAGE)(),  # local env friendly
        validators=[
            FileValidator(
                max_size_setting_name="MESSAGE_ATTACHMENT_MAX_UPLOAD_SIZE",  # tests friendly
            )
        ],
    )

    class Meta:
        default_permissions = ()

    def __str__(self):
        return self.filename

    def get_absolute_url(self):
        return reverse("chat:attachment", kwargs={"pk": self.pk})

    @property
    def filename(self):
        return os.path.basename(self.file.name)


class MessageNotification(models.Model):
    deal = models.ForeignKey(
        "deals.Deal",
        on_delete=models.CASCADE,
        related_name="notifications",
    )
    user = models.ForeignKey(
        "registration.User",
        on_delete=models.CASCADE,
    )
    last_message_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=["last_message_at"]),
        ]
        ordering = ["-last_message_at"]
        default_permissions = ()
        unique_together = [["deal", "user"]]
