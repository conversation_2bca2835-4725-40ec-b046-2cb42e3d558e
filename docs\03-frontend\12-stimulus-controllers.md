# Lesson 12: JavaScript & Stimulus Controllers 🎮

## 🎯 Learning Objectives
By the end of this lesson, you will understand:
- Advanced Stimulus controller patterns
- Real-world controller implementations in Crelancer
- Event handling and data flow
- Integration with third-party libraries
- Testing Stimulus controllers

## 🎮 Advanced Stimulus Patterns

### **Chat Controller - Real-time Messaging**
```javascript
// frontend/src/controllers/chat_controller.js
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["messagesContainer", "messageInput", "sendButton", "typingIndicator"]
  static values = { 
    dealId: Number,
    userRole: String,
    wsUrl: String
  }
  
  connect() {
    this.setupWebSocket()
    this.scrollToBottom()
    this.setupTypingIndicator()
    this.messageInputTarget.focus()
  }
  
  disconnect() {
    if (this.websocket) {
      this.websocket.close()
    }
    this.clearTypingTimeout()
  }
  
  // WebSocket setup for real-time messaging
  setupWebSocket() {
    if (!this.wsUrlValue) return
    
    this.websocket = new WebSocket(this.wsUrlValue)
    
    this.websocket.onmessage = (event) => {
      const data = JSON.parse(event.data)
      this.handleWebSocketMessage(data)
    }
    
    this.websocket.onclose = () => {
      // Reconnect after 3 seconds
      setTimeout(() => this.setupWebSocket(), 3000)
    }
  }
  
  // Handle incoming WebSocket messages
  handleWebSocketMessage(data) {
    switch (data.type) {
      case 'new_message':
        this.appendMessage(data.message)
        break
      case 'typing_start':
        this.showTypingIndicator(data.user)
        break
      case 'typing_stop':
        this.hideTypingIndicator()
        break
      case 'message_read':
        this.markMessageAsRead(data.messageId)
        break
    }
  }
  
  // Send message
  sendMessage(event) {
    event.preventDefault()
    
    const content = this.messageInputTarget.value.trim()
    if (!content) return
    
    this.disableForm()
    
    // Send via WebSocket if available, otherwise use HTTP
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify({
        type: 'send_message',
        content: content,
        deal_id: this.dealIdValue
      }))
      this.clearInput()
      this.enableForm()
    } else {
      this.sendMessageHTTP(event.target, content)
    }
  }
  
  // Fallback HTTP message sending
  sendMessageHTTP(form, content) {
    const formData = new FormData(form)
    
    fetch(form.action, {
      method: 'POST',
      body: formData,
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'Accept': 'text/vnd.turbo-stream.html'
      }
    })
    .then(response => response.text())
    .then(html => {
      // Turbo will handle the stream response
      this.clearInput()
      this.enableForm()
      this.scrollToBottom()
    })
    .catch(error => {
      console.error('Error sending message:', error)
      this.enableForm()
    })
  }
  
  // Typing indicator
  handleTyping() {
    this.clearTypingTimeout()
    
    // Send typing start event
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify({
        type: 'typing_start',
        deal_id: this.dealIdValue
      }))
    }
    
    // Stop typing after 3 seconds
    this.typingTimeout = setTimeout(() => {
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        this.websocket.send(JSON.stringify({
          type: 'typing_stop',
          deal_id: this.dealIdValue
        }))
      }
    }, 3000)
  }
  
  // File upload handling
  handleFileUpload(event) {
    const file = event.target.files[0]
    if (!file) return
    
    // Validate file size (10MB max)
    if (file.size > 10 * 1024 * 1024) {
      alert('File size must be less than 10MB')
      return
    }
    
    // Show upload progress
    this.showUploadProgress()
    
    const formData = new FormData()
    formData.append('file', file)
    formData.append('deal_id', this.dealIdValue)
    
    fetch('/chat/upload/', {
      method: 'POST',
      body: formData,
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        this.appendMessage(data.message)
      } else {
        alert('Upload failed: ' + data.error)
      }
      this.hideUploadProgress()
    })
    .catch(error => {
      console.error('Upload error:', error)
      this.hideUploadProgress()
    })
  }
  
  // Helper methods
  appendMessage(messageHtml) {
    this.messagesContainerTarget.insertAdjacentHTML('beforeend', messageHtml)
    this.scrollToBottom()
  }
  
  scrollToBottom() {
    const container = this.messagesContainerTarget
    container.scrollTop = container.scrollHeight
  }
  
  clearInput() {
    this.messageInputTarget.value = ''
  }
  
  disableForm() {
    this.messageInputTarget.disabled = true
    this.sendButtonTarget.disabled = true
  }
  
  enableForm() {
    this.messageInputTarget.disabled = false
    this.sendButtonTarget.disabled = false
    this.messageInputTarget.focus()
  }
}
```

### **Job Search Controller - Advanced Filtering**
```javascript
// frontend/src/controllers/job_search_controller.js
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["form", "results", "loading", "noResults", "filterCount"]
  static values = { 
    url: String,
    debounceDelay: { type: Number, default: 300 },
    currentPage: { type: Number, default: 1 }
  }
  
  connect() {
    this.timeout = null
    this.abortController = null
    this.setupInfiniteScroll()
    this.updateFilterCount()
  }
  
  // Debounced search with abort controller
  search() {
    clearTimeout(this.timeout)
    
    // Cancel previous request
    if (this.abortController) {
      this.abortController.abort()
    }
    
    this.timeout = setTimeout(() => {
      this.performSearch()
    }, this.debounceDelayValue)
  }
  
  performSearch(loadMore = false) {
    const formData = new FormData(this.formTarget)
    
    if (loadMore) {
      formData.append('page', this.currentPageValue + 1)
    } else {
      this.currentPageValue = 1
      formData.append('page', 1)
    }
    
    const params = new URLSearchParams(formData)
    
    // Create new abort controller
    this.abortController = new AbortController()
    
    this.showLoading()
    
    fetch(`${this.urlValue}?${params}`, {
      headers: {
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      },
      signal: this.abortController.signal
    })
    .then(response => response.json())
    .then(data => {
      if (loadMore) {
        this.appendResults(data.html)
        this.currentPageValue++
      } else {
        this.replaceResults(data.html)
      }
      
      this.updatePagination(data.has_next)
      this.hideLoading()
      this.updateFilterCount()
    })
    .catch(error => {
      if (error.name !== 'AbortError') {
        console.error('Search error:', error)
        this.hideLoading()
      }
    })
  }
  
  // Infinite scroll setup
  setupInfiniteScroll() {
    this.intersectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && this.hasMorePages) {
          this.performSearch(true)
        }
      })
    }, {
      rootMargin: '100px'
    })
    
    // Observe the last result item
    this.observeLastItem()
  }
  
  observeLastItem() {
    const lastItem = this.resultsTarget.querySelector('.job-card:last-child')
    if (lastItem) {
      this.intersectionObserver.observe(lastItem)
    }
  }
  
  // Filter management
  addFilter(event) {
    const filterType = event.params.type
    const filterValue = event.params.value
    
    // Add hidden input for filter
    const input = document.createElement('input')
    input.type = 'hidden'
    input.name = filterType
    input.value = filterValue
    this.formTarget.appendChild(input)
    
    this.search()
    this.updateFilterTags()
  }
  
  removeFilter(event) {
    const filterType = event.params.type
    const filterValue = event.params.value
    
    // Remove matching hidden inputs
    const inputs = this.formTarget.querySelectorAll(`input[name="${filterType}"][value="${filterValue}"]`)
    inputs.forEach(input => input.remove())
    
    this.search()
    this.updateFilterTags()
  }
  
  clearAllFilters() {
    // Reset form
    this.formTarget.reset()
    
    // Remove all hidden filter inputs
    const hiddenInputs = this.formTarget.querySelectorAll('input[type="hidden"]')
    hiddenInputs.forEach(input => {
      if (input.name !== 'csrfmiddlewaretoken') {
        input.remove()
      }
    })
    
    this.search()
    this.updateFilterTags()
  }
  
  // Save search
  saveSearch() {
    const formData = new FormData(this.formTarget)
    const searchName = prompt('Enter a name for this search:')
    
    if (!searchName) return
    
    formData.append('name', searchName)
    
    fetch('/jobs/save-search/', {
      method: 'POST',
      body: formData,
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        alert('Search saved successfully!')
      } else {
        alert('Error saving search: ' + data.error)
      }
    })
  }
  
  // Helper methods
  replaceResults(html) {
    this.resultsTarget.innerHTML = html
    this.observeLastItem()
  }
  
  appendResults(html) {
    this.resultsTarget.insertAdjacentHTML('beforeend', html)
    this.observeLastItem()
  }
  
  updateFilterCount() {
    const formData = new FormData(this.formTarget)
    const activeFilters = Array.from(formData.entries()).filter(([key, value]) => 
      key !== 'csrfmiddlewaretoken' && value !== ''
    ).length
    
    if (this.hasFilterCountTarget) {
      this.filterCountTarget.textContent = activeFilters
      this.filterCountTarget.classList.toggle('hidden', activeFilters === 0)
    }
  }
  
  showLoading() {
    this.loadingTarget.classList.remove('hidden')
  }
  
  hideLoading() {
    this.loadingTarget.classList.add('hidden')
  }
}
```

### **Payment Controller - Stripe Integration**
```javascript
// frontend/src/controllers/payment_controller.js
import { Controller } from "@hotwired/stimulus"
import { loadStripe } from "@stripe/stripe-js"

export default class extends Controller {
  static targets = ["form", "cardElement", "submitButton", "errors"]
  static values = { 
    publicKey: String,
    clientSecret: String,
    amount: Number
  }
  
  async connect() {
    this.stripe = await loadStripe(this.publicKeyValue)
    this.elements = this.stripe.elements()
    this.setupCardElement()
  }
  
  setupCardElement() {
    // Create card element
    this.cardElement = this.elements.create('card', {
      style: {
        base: {
          fontSize: '16px',
          color: '#424770',
          '::placeholder': {
            color: '#aab7c4',
          },
        },
        invalid: {
          color: '#9e2146',
        },
      },
    })
    
    // Mount card element
    this.cardElement.mount(this.cardElementTarget)
    
    // Handle real-time validation errors
    this.cardElement.on('change', (event) => {
      if (event.error) {
        this.showError(event.error.message)
      } else {
        this.clearErrors()
      }
      
      // Enable/disable submit button
      this.submitButtonTarget.disabled = !event.complete
    })
  }
  
  async submitPayment(event) {
    event.preventDefault()
    
    this.setLoading(true)
    this.clearErrors()
    
    // Confirm payment with Stripe
    const { error, paymentIntent } = await this.stripe.confirmCardPayment(
      this.clientSecretValue,
      {
        payment_method: {
          card: this.cardElement,
          billing_details: {
            name: this.formTarget.querySelector('[name="cardholder_name"]').value,
            email: this.formTarget.querySelector('[name="email"]').value,
          },
        }
      }
    )
    
    if (error) {
      this.showError(error.message)
      this.setLoading(false)
    } else {
      // Payment succeeded
      this.handlePaymentSuccess(paymentIntent)
    }
  }
  
  handlePaymentSuccess(paymentIntent) {
    // Send confirmation to server
    fetch('/finance/payment-success/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': this.getCSRFToken(),
      },
      body: JSON.stringify({
        payment_intent_id: paymentIntent.id,
        deal_id: this.data.get('dealId')
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        window.location.href = data.redirect_url
      } else {
        this.showError('Payment processing failed. Please try again.')
        this.setLoading(false)
      }
    })
    .catch(error => {
      console.error('Error:', error)
      this.showError('An unexpected error occurred.')
      this.setLoading(false)
    })
  }
  
  setLoading(loading) {
    this.submitButtonTarget.disabled = loading
    
    if (loading) {
      this.submitButtonTarget.innerHTML = `
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Processing...
      `
    } else {
      this.submitButtonTarget.innerHTML = `Pay $${this.amountValue}`
    }
  }
  
  showError(message) {
    this.errorsTarget.textContent = message
    this.errorsTarget.classList.remove('hidden')
  }
  
  clearErrors() {
    this.errorsTarget.textContent = ''
    this.errorsTarget.classList.add('hidden')
  }
  
  getCSRFToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]').value
  }
}
```

### **File Upload Controller - Drag & Drop**
```javascript
// frontend/src/controllers/file_upload_controller.js
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["input", "preview", "progress", "error"]
  static values = { 
    maxSize: { type: Number, default: 10485760 }, // 10MB
    allowedTypes: { type: Array, default: ["image/*", "application/pdf"] },
    uploadUrl: String
  }
  
  connect() {
    this.setupDragAndDrop()
  }
  
  setupDragAndDrop() {
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      this.element.addEventListener(eventName, this.preventDefaults.bind(this), false)
    })
    
    ;['dragenter', 'dragover'].forEach(eventName => {
      this.element.addEventListener(eventName, this.highlight.bind(this), false)
    })
    
    ;['dragleave', 'drop'].forEach(eventName => {
      this.element.addEventListener(eventName, this.unhighlight.bind(this), false)
    })
    
    this.element.addEventListener('drop', this.handleDrop.bind(this), false)
  }
  
  preventDefaults(e) {
    e.preventDefault()
    e.stopPropagation()
  }
  
  highlight() {
    this.element.classList.add('border-primary-500', 'bg-primary-50')
  }
  
  unhighlight() {
    this.element.classList.remove('border-primary-500', 'bg-primary-50')
  }
  
  handleDrop(e) {
    const files = e.dataTransfer.files
    this.handleFiles(files)
  }
  
  handleFileSelect(event) {
    const files = event.target.files
    this.handleFiles(files)
  }
  
  handleFiles(files) {
    Array.from(files).forEach(file => {
      if (this.validateFile(file)) {
        this.uploadFile(file)
      }
    })
  }
  
  validateFile(file) {
    // Check file size
    if (file.size > this.maxSizeValue) {
      this.showError(`File "${file.name}" is too large. Maximum size is ${this.formatFileSize(this.maxSizeValue)}.`)
      return false
    }
    
    // Check file type
    const isValidType = this.allowedTypesValue.some(type => {
      if (type.endsWith('/*')) {
        return file.type.startsWith(type.slice(0, -1))
      }
      return file.type === type
    })
    
    if (!isValidType) {
      this.showError(`File "${file.name}" is not a supported file type.`)
      return false
    }
    
    return true
  }
  
  uploadFile(file) {
    const formData = new FormData()
    formData.append('file', file)
    
    // Create preview
    const previewElement = this.createPreview(file)
    this.previewTarget.appendChild(previewElement)
    
    // Upload with progress
    const xhr = new XMLHttpRequest()
    
    xhr.upload.addEventListener('progress', (e) => {
      if (e.lengthComputable) {
        const percentComplete = (e.loaded / e.total) * 100
        this.updateProgress(previewElement, percentComplete)
      }
    })
    
    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        const response = JSON.parse(xhr.responseText)
        this.handleUploadSuccess(previewElement, response)
      } else {
        this.handleUploadError(previewElement, 'Upload failed')
      }
    })
    
    xhr.addEventListener('error', () => {
      this.handleUploadError(previewElement, 'Upload failed')
    })
    
    xhr.open('POST', this.uploadUrlValue)
    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest')
    xhr.setRequestHeader('X-CSRFToken', this.getCSRFToken())
    xhr.send(formData)
  }
  
  createPreview(file) {
    const div = document.createElement('div')
    div.className = 'flex items-center p-3 bg-gray-50 rounded-lg'
    
    div.innerHTML = `
      <div class="flex-shrink-0">
        ${this.getFileIcon(file.type)}
      </div>
      <div class="flex-1 ml-3">
        <p class="text-sm font-medium text-gray-900">${file.name}</p>
        <p class="text-sm text-gray-500">${this.formatFileSize(file.size)}</p>
        <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
          <div class="bg-primary-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
        </div>
      </div>
      <div class="flex-shrink-0 ml-3">
        <button type="button" class="text-gray-400 hover:text-gray-600" data-action="click->file-upload#removeFile">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
          </svg>
        </button>
      </div>
    `
    
    return div
  }
  
  updateProgress(element, percent) {
    const progressBar = element.querySelector('.bg-primary-600')
    progressBar.style.width = `${percent}%`
  }
  
  handleUploadSuccess(element, response) {
    element.classList.add('border-green-200', 'bg-green-50')
    element.dataset.fileId = response.file_id
    
    // Hide progress bar
    const progressContainer = element.querySelector('.bg-gray-200')
    progressContainer.classList.add('hidden')
    
    // Show success icon
    const iconContainer = element.querySelector('.flex-shrink-0')
    iconContainer.innerHTML = `
      <svg class="w-6 h-6 text-green-500" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
      </svg>
    `
  }
  
  handleUploadError(element, message) {
    element.classList.add('border-red-200', 'bg-red-50')
    this.showError(message)
  }
  
  removeFile(event) {
    const element = event.target.closest('.flex')
    const fileId = element.dataset.fileId
    
    if (fileId) {
      // Delete from server
      fetch(`/files/${fileId}/delete/`, {
        method: 'DELETE',
        headers: {
          'X-CSRFToken': this.getCSRFToken()
        }
      })
    }
    
    element.remove()
  }
  
  // Helper methods
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
  
  getFileIcon(mimeType) {
    if (mimeType.startsWith('image/')) {
      return '<svg class="w-6 h-6 text-blue-500" fill="currentColor" viewBox="0 0 20 20">...</svg>'
    } else if (mimeType === 'application/pdf') {
      return '<svg class="w-6 h-6 text-red-500" fill="currentColor" viewBox="0 0 20 20">...</svg>'
    }
    return '<svg class="w-6 h-6 text-gray-500" fill="currentColor" viewBox="0 0 20 20">...</svg>'
  }
  
  showError(message) {
    this.errorTarget.textContent = message
    this.errorTarget.classList.remove('hidden')
    
    setTimeout(() => {
      this.errorTarget.classList.add('hidden')
    }, 5000)
  }
  
  getCSRFToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]').value
  }
}
```

## 🧪 Testing Stimulus Controllers

### **Controller Testing Setup**
```javascript
// tests/controllers/chat_controller.test.js
import { Application } from "@hotwired/stimulus"
import ChatController from "../../src/controllers/chat_controller"

describe("ChatController", () => {
  let application
  let controller
  let element
  
  beforeEach(() => {
    // Setup DOM
    document.body.innerHTML = `
      <div data-controller="chat" 
           data-chat-deal-id-value="123"
           data-chat-user-role-value="talent">
        <div data-chat-target="messagesContainer"></div>
        <input data-chat-target="messageInput" type="text">
        <button data-chat-target="sendButton">Send</button>
      </div>
    `
    
    // Setup Stimulus
    application = Application.start()
    application.register("chat", ChatController)
    
    element = document.querySelector('[data-controller="chat"]')
    controller = application.getControllerForElementAndIdentifier(element, "chat")
  })
  
  afterEach(() => {
    application.stop()
    document.body.innerHTML = ""
  })
  
  test("connects successfully", () => {
    expect(controller).toBeDefined()
    expect(controller.dealIdValue).toBe(123)
    expect(controller.userRoleValue).toBe("talent")
  })
  
  test("sends message", async () => {
    const mockFetch = jest.fn().mockResolvedValue({
      text: () => Promise.resolve('<div>Message sent</div>')
    })
    global.fetch = mockFetch
    
    controller.messageInputTarget.value = "Hello world"
    
    const form = document.createElement('form')
    form.action = '/chat/send/'
    const event = { preventDefault: jest.fn(), target: form }
    
    await controller.sendMessage(event)
    
    expect(event.preventDefault).toHaveBeenCalled()
    expect(mockFetch).toHaveBeenCalled()
    expect(controller.messageInputTarget.value).toBe("")
  })
})
```

## 🎯 Key Takeaways

1. **Controller Lifecycle**: Connect, disconnect, and target management
2. **Real-time Features**: WebSocket integration for live updates
3. **Third-party Integration**: Stripe, file uploads, and external APIs
4. **Error Handling**: Graceful degradation and user feedback
5. **Performance**: Debouncing, abort controllers, and efficient DOM updates
6. **Testing**: Unit tests for controller behavior and interactions

## 🔗 What's Next?

Now that you understand Stimulus controllers, let's explore Webpack configuration and how assets are built and optimized.

**Next**: [Lesson 13: Webpack Configuration & Asset Management](./13-webpack-assets.md)

---

## 💡 Quick Quiz

1. How do Stimulus controllers handle cleanup when disconnecting?
2. What's the purpose of abort controllers in AJAX requests?
3. How do you integrate third-party libraries with Stimulus?
4. What's the best way to handle real-time updates in Stimulus?

*Answers: 1) disconnect() method for cleanup, 2) Cancel previous requests to prevent race conditions, 3) Import in controller and setup in connect(), 4) WebSocket integration with fallback to HTTP*
