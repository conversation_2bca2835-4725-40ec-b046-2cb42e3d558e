# Lesson 3: Development Environment Setup 🛠️

## 🎯 Learning Objectives
By the end of this lesson, you will:
- Have a fully functional Crelancer development environment
- Understand the Docker setup and configuration
- Know how to run and debug the application
- Be able to access all parts of the system

## 📋 Prerequisites

### **Required Software**
- **Docker Desktop** (Windows/Mac) or **Docker Engine** (Linux)
- **Git** for version control
- **Code Editor** (VS Code recommended)
- **Web Browser** (Chrome/Firefox recommended)

### **Optional but Recommended**
- **PyCharm Professional** (for advanced Django debugging)
- **Postman** (for API testing)
- **pgAdmin** (for database management)

## 🚀 Step-by-Step Setup

### **Step 1: Clone the Repository**
```bash
# Clone the project
git clone <repository-url>
cd crelancer

# Check the project structure
ls -la
```

### **Step 2: Prepare Environment Files**

The project uses different configurations for different environments. Let's set up the local development environment:

```bash
# Copy local development files to root
cp ./deploy/local/.env ./.env
cp ./deploy/local/docker-compose.yml ./docker-compose.yml
cp ./deploy/local/Dockerfile ./Dockerfile
cp ./deploy/local/entrypoint.sh ./entrypoint.sh
```

### **Step 3: Configure Environment Variables**

Edit the `.env` file with your local settings:

```bash
# .env file example
DEBUG=True
SECRET_KEY=your-secret-key-here
DATABASE_URL=************************************/crelancer

# Stripe Configuration (for testing)
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Email Configuration (development)
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend

# Storage Configuration
USE_S3=False
STATIC_ROOT=/app/staticfiles
MEDIA_ROOT=/app/media
```

**Important Environment Variables:**

| Variable | Purpose | Example |
|----------|---------|---------|
| `DEBUG` | Enable Django debug mode | `True` |
| `SECRET_KEY` | Django security key | `your-secret-key` |
| `DATABASE_URL` | PostgreSQL connection | `postgres://user:pass@host:port/db` |
| `STRIPE_*` | Payment processing | Stripe test keys |
| `EMAIL_BACKEND` | Email delivery method | Console backend for dev |

### **Step 4: Build and Start Services**

```bash
# Build and start all services
docker-compose up -d --build

# Check if services are running
docker-compose ps
```

You should see services like:
- `crelancer_web` - Django application
- `crelancer_db` - PostgreSQL database
- `crelancer_redis` - Redis cache (if configured)

### **Step 5: Database Setup**

```bash
# Run database migrations
docker-compose exec crelancer_web python manage.py migrate

# Create a superuser account
docker-compose exec crelancer_web python manage.py createsuperuser

# Load initial data (categories, countries, languages)
docker-compose exec crelancer_web python manage.py loaddata backend/apps/taxonomy/fixtures/languages.json
docker-compose exec crelancer_web python manage.py loaddata backend/apps/taxonomy/fixtures/countries.json
docker-compose exec crelancer_web python manage.py loaddata backend/apps/taxonomy/fixtures/categories.json
```

### **Step 6: Frontend Asset Setup**

```bash
# Install Node.js dependencies
docker-compose exec crelancer_web npm install

# Start webpack development server (in a new terminal)
docker-compose exec crelancer_web npm run start
```

This starts the webpack dev server with hot reloading for frontend assets.

## 🌐 Accessing the Application

### **Main Application**
- **URL**: http://localhost:8000
- **Description**: Main Crelancer application

### **Django Admin**
- **URL**: http://localhost:8000/admin
- **Credentials**: Use the superuser account you created
- **Purpose**: Manage users, jobs, deals, etc.

### **Database Access**
```bash
# Connect to PostgreSQL directly
docker-compose exec crelancer_db psql -U postgres -d crelancer

# Or use a GUI tool like pgAdmin
# Host: localhost
# Port: 5432
# Database: crelancer
# Username: postgres
# Password: (from your .env file)
```

## 🔧 Development Workflow

### **Starting Development**
```bash
# Start all services
docker-compose up -d

# Start webpack dev server (for hot reloading)
docker-compose exec crelancer_web npm run start

# View logs
docker-compose logs -f crelancer_web
```

### **Making Code Changes**

1. **Backend Changes**: Django auto-reloads when you save Python files
2. **Frontend Changes**: Webpack dev server auto-reloads CSS/JS changes
3. **Template Changes**: Django auto-reloads template changes

### **Running Commands**
```bash
# Django management commands
docker-compose exec crelancer_web python manage.py <command>

# Examples:
docker-compose exec crelancer_web python manage.py shell
docker-compose exec crelancer_web python manage.py makemigrations
docker-compose exec crelancer_web python manage.py test

# NPM commands
docker-compose exec crelancer_web npm run <script>

# Examples:
docker-compose exec crelancer_web npm run build
docker-compose exec crelancer_web npm run watch
```

## 🐛 Debugging Setup

### **Django Debug Toolbar**
The project includes Django Debug Toolbar for development:
- Shows SQL queries
- Template rendering time
- Cache usage
- Request/response headers

### **VS Code Setup**
Create `.vscode/launch.json` for debugging:
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python: Django",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/manage.py",
      "args": ["runserver", "0.0.0.0:8000"],
      "django": true,
      "justMyCode": false
    }
  ]
}
```

### **PyCharm Setup**
1. **Add Docker Compose Interpreter**:
   - File → Settings → Project → Python Interpreter
   - Add → Docker Compose
   - Service: `crelancer_web`

2. **Configure Django Settings**:
   - Languages & Frameworks → Django
   - Enable Django Support
   - Django project root: `/app`
   - Settings: `backend.settings.local`

## 📊 Monitoring and Logs

### **Application Logs**
```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f crelancer_web
docker-compose logs -f crelancer_db

# View last 100 lines
docker-compose logs --tail=100 crelancer_web
```

### **Database Monitoring**
```sql
-- Check active connections
SELECT * FROM pg_stat_activity WHERE datname = 'crelancer';

-- Check table sizes
SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables WHERE schemaname = 'public' ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

## 🧪 Testing Setup

### **Running Tests**
```bash
# Run all tests
docker-compose exec crelancer_web python manage.py test

# Run specific app tests
docker-compose exec crelancer_web python manage.py test backend.apps.jobs

# Run with coverage
docker-compose exec crelancer_web coverage run --source='.' manage.py test
docker-compose exec crelancer_web coverage report
```

### **Test Database**
Tests automatically create and destroy a test database. No additional setup required.

## 🔒 Stripe Testing Setup

### **Test Cards**
Use these test card numbers:
- **Success**: `****************`
- **Decline**: `****************`
- **3D Secure**: `****************`

### **Webhook Testing**
```bash
# Install Stripe CLI
# Download from: https://stripe.com/docs/stripe-cli

# Login to Stripe
stripe login

# Forward webhooks to local development
stripe listen --forward-to localhost:8000/finance/stripe_webhook/ \
  --events account.updated,charge.refund.updated,payment_intent.payment_failed,payment_intent.requires_action,payment_intent.succeeded
```

## ⚠️ Common Issues and Solutions

### **Port Already in Use**
```bash
# Check what's using port 8000
netstat -tulpn | grep :8000

# Kill the process or change port in docker-compose.yml
```

### **Database Connection Issues**
```bash
# Reset database
docker-compose down -v
docker-compose up -d --build
docker-compose exec crelancer_web python manage.py migrate
```

### **Node Modules Issues**
```bash
# Clear node modules and reinstall
docker-compose exec crelancer_web rm -rf node_modules
docker-compose exec crelancer_web npm install
```

### **Permission Issues (Linux/Mac)**
```bash
# Fix file permissions
sudo chown -R $USER:$USER .
```

## ✅ Verification Checklist

After setup, verify everything works:

- [ ] Application loads at http://localhost:8000
- [ ] Admin panel accessible at http://localhost:8000/admin
- [ ] Can create and login with superuser account
- [ ] Database contains initial fixture data
- [ ] Webpack dev server running and hot-reloading works
- [ ] Can create a new user account
- [ ] Email appears in console logs
- [ ] Static files load correctly

## 🎯 Key Takeaways

1. **Docker Compose** orchestrates all services for consistent development
2. **Environment variables** configure the application for different environments
3. **Webpack dev server** provides hot reloading for frontend development
4. **Django management commands** handle database operations and testing
5. **Stripe CLI** enables local webhook testing
6. **Proper logging** helps debug issues during development

## 🔗 What's Next?

Now that your environment is set up, let's explore the project structure and understand how the code is organized.

**Next**: [Lesson 4: Project Structure & Organization](./04-project-structure.md)

---

## 💡 Quick Quiz

1. Which command starts all Docker services?
2. Where are environment variables stored?
3. What port does the application run on by default?
4. How do you access the Django admin panel?

*Answers: 1) docker-compose up -d, 2) .env file, 3) 8000, 4) http://localhost:8000/admin*
