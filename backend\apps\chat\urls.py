from django.urls import path, include

from chat import views as chat_views

app_name = "chat"
urlpatterns = [
    path("", chat_views.MainView.as_view(), name="main"),
    path(
        "unread-count/",
        chat_views.DealsUnreadExistView.as_view(),
        name="unread_exists",
    ),
    path(
        "<int:deal_id>/",
        include(
            [
                path("", chat_views.MessagesListView.as_view(), name="message_list"),
                path(
                    "loadmore/",
                    chat_views.MessagesListStreamView.as_view(),
                    name="message_list_loadmore",
                ),
                path(
                    "create/",
                    chat_views.MessageCreateView.as_view(),
                    name="message_create",
                ),
            ],
        ),
    ),
    path(
        "attachments/<int:pk>/",
        chat_views.MessageAttachmentView.as_view(),
        name="attachment",
    ),
]
