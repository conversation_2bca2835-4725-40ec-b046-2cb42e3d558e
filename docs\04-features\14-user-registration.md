# Lesson 14: User Registration & Verification 👤

## 🎯 Learning Objectives
- Complete user registration flow
- Email verification system
- Role-based profile creation
- Account security and validation
- User onboarding experience

## 📝 Registration Flow Overview

### **Registration Process**
```
1. Role Selection (Talent/Client)
2. Account Creation Form
3. Email Verification
4. Profile Completion
5. Account Activation
```

### **User Model Structure**
```python
class User(AbstractUser):
    email = LowercaseEmailField(unique=True)
    role = models.CharField(choices=RoleChoices.choices)
    is_email_confirmed = models.BooleanField(default=False)
    
    # Profile completion tracking
    profile_completion_step = models.IntegerField(default=0)
    onboarding_completed = models.BooleanField(default=False)
```

## 🔐 Email Verification System

### **Token Generation**
```python
from django.contrib.auth.tokens import PasswordResetTokenGenerator

class AccountActivationTokenGenerator(PasswordResetTokenGenerator):
    def _make_hash_value(self, user, timestamp):
        return (
            str(user.pk) + str(timestamp) +
            str(user.is_email_confirmed)
        )

account_activation_token = AccountActivationTokenGenerator()
```

### **Email Verification View**
```python
class SignupConfirmView(TemplateView):
    def get(self, request, uidb64, token):
        try:
            uid = force_str(urlsafe_base64_decode(uidb64))
            user = User.objects.get(pk=uid)
        except (TypeError, ValueError, User.DoesNotExist):
            user = None
        
        if user and account_activation_token.check_token(user, token):
            user.is_active = True
            user.is_email_confirmed = True
            user.save()
            
            login(request, user)
            return redirect(self.get_onboarding_url(user))
        
        return render(request, 'registration/invalid_link.html')
```

## 👨‍💻 Talent Registration

### **Talent Signup Form**
```python
class TalentSignupForm(SignupForm):
    professional_title = forms.CharField(max_length=255, required=False)
    skills = forms.ModelMultipleChoiceField(
        queryset=Skill.objects.all(),
        widget=forms.CheckboxSelectMultiple,
        required=False
    )
    
    def save(self, commit=True):
        user = super().save(commit)
        if commit:
            talent = Talent.objects.create(
                user=user,
                professional_title=self.cleaned_data.get('professional_title', '')
            )
            talent.skills.set(self.cleaned_data.get('skills', []))
        return user
```

### **Talent Profile Completion**
```python
class TalentProfileWizard(SessionWizardView):
    template_name = 'talents/profile_wizard.html'
    form_list = [
        ('basic', TalentBasicInfoForm),
        ('skills', TalentSkillsForm),
        ('experience', TalentExperienceForm),
        ('portfolio', TalentPortfolioForm),
        ('verification', TalentVerificationForm),
    ]
    
    def done(self, form_list, **kwargs):
        talent = self.request.user.talent
        
        for form in form_list:
            for field, value in form.cleaned_data.items():
                if hasattr(talent, field):
                    setattr(talent, field, value)
        
        talent.is_published = True
        talent.save()
        
        return redirect('talents:dashboard')
```

## 🏢 Client Registration

### **Client Signup Form**
```python
class ClientSignupForm(SignupForm):
    company_name = forms.CharField(max_length=255, required=False)
    website = forms.URLField(required=False)
    company_size = forms.ChoiceField(
        choices=[
            ('1-10', '1-10 employees'),
            ('11-50', '11-50 employees'),
            ('51-200', '51-200 employees'),
            ('200+', '200+ employees'),
        ],
        required=False
    )
    
    def save(self, commit=True):
        user = super().save(commit)
        if commit:
            Client.objects.create(
                user=user,
                company_name=self.cleaned_data.get('company_name', ''),
                website=self.cleaned_data.get('website', ''),
                company_size=self.cleaned_data.get('company_size', '')
            )
        return user
```

## ✅ Verification System

### **Document Upload**
```python
class VerificationDocument(models.Model):
    talent = models.ForeignKey(Talent, on_delete=models.CASCADE)
    document_type = models.CharField(choices=DOCUMENT_TYPES)
    file = models.FileField(upload_to='verification/')
    status = models.CharField(choices=STATUS_CHOICES, default='pending')
    uploaded_at = models.DateTimeField(auto_now_add=True)
    reviewed_at = models.DateTimeField(null=True)
    reviewer_notes = models.TextField(blank=True)
```

### **Verification Process**
```python
class TalentVerificationView(TalentRequiredMixin, FormView):
    form_class = VerificationDocumentForm
    template_name = 'talents/verification.html'
    
    def form_valid(self, form):
        document = form.save(commit=False)
        document.talent = self.request.user.talent
        document.save()
        
        # Notify admin for review
        send_verification_notification.delay(document.id)
        
        messages.success(
            self.request,
            "Documents uploaded successfully. Review typically takes 24-48 hours."
        )
        return redirect('talents:verification_status')
```

## 🎯 Key Takeaways

1. **Secure Registration**: Email verification and token-based activation
2. **Role-based Onboarding**: Different flows for talents and clients
3. **Progressive Completion**: Step-by-step profile building
4. **Verification System**: Document upload and admin review process
5. **User Experience**: Clear guidance and progress tracking

## 🔗 What's Next?

**Next**: [Lesson 15: Job Posting & Management](./15-job-management.md)

---

## 💡 Quick Quiz

1. How does email verification work in the registration process?
2. What's the difference between talent and client registration?
3. How is the verification system implemented?
4. What happens after successful email confirmation?

*Answers: 1) Token-based verification with email links, 2) Different forms and profile creation based on role, 3) Document upload with admin review workflow, 4) User login and redirect to onboarding*
