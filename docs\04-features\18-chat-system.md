# Lesson 18: Chat & Communication 💬

## 🎯 Learning Objectives
- Real-time messaging system
- File sharing and attachments
- Message status tracking
- Notification integration
- Chat performance optimization

## 💬 Message Model Structure

```python
class Message(models.Model):
    class MessageType(models.TextChoices):
        TEXT = "text", "Text"
        SYSTEM = "system", "System"
        FILE = "file", "File"
        IMAGE = "image", "Image"
    
    deal = models.ForeignKey(Deal, on_delete=models.CASCADE, related_name="messages")
    sender = models.ForeignKey(User, on_delete=models.CASCADE)
    
    # Content
    message_type = models.CharField(max_length=10, choices=MessageType.choices)
    content = models.TextField()
    
    # Attachments
    attachment = models.FileField(upload_to="chat/files/%Y/%m/", null=True)
    attachment_name = models.CharField(max_length=255, blank=True)
    attachment_size = models.PositiveIntegerField(null=True)
    
    # Status
    created_at = models.DateTimeField(auto_now_add=True)
    edited_at = models.DateTimeField(null=True)
    is_edited = models.BooleanField(default=False)
    
    # Read status
    read_by_client = models.BooleanField(default=False)
    read_by_talent = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True)
```

## 🔄 Real-time Messaging

### **WebSocket Consumer**
```python
import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async

class ChatConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        self.deal_id = self.scope['url_route']['kwargs']['deal_id']
        self.room_group_name = f'chat_{self.deal_id}'
        
        # Check permissions
        if not await self.has_permission():
            await self.close()
            return
        
        # Join room group
        await self.channel_layer.group_add(
            self.room_group_name,
            self.channel_name
        )
        
        await self.accept()
    
    async def disconnect(self, close_code):
        # Leave room group
        await self.channel_layer.group_discard(
            self.room_group_name,
            self.channel_name
        )
    
    async def receive(self, text_data):
        data = json.loads(text_data)
        message_type = data['type']
        
        if message_type == 'send_message':
            await self.send_message(data)
        elif message_type == 'typing_start':
            await self.typing_start(data)
        elif message_type == 'typing_stop':
            await self.typing_stop(data)
        elif message_type == 'mark_read':
            await self.mark_read(data)
    
    async def send_message(self, data):
        content = data['content']
        
        # Save message to database
        message = await self.create_message(content)
        
        # Send message to room group
        await self.channel_layer.group_send(
            self.room_group_name,
            {
                'type': 'chat_message',
                'message': await self.serialize_message(message)
            }
        )
    
    async def chat_message(self, event):
        # Send message to WebSocket
        await self.send(text_data=json.dumps({
            'type': 'new_message',
            'message': event['message']
        }))
    
    @database_sync_to_async
    def create_message(self, content):
        return Message.objects.create(
            deal_id=self.deal_id,
            sender=self.scope['user'],
            content=content,
            message_type=Message.MessageType.TEXT
        )
```

### **Chat View**
```python
class ChatView(DealParticipantMixin, DetailView):
    model = Deal
    template_name = "chat/chat.html"
    context_object_name = "deal"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get messages with pagination
        messages = self.object.messages.select_related('sender').order_by('created_at')
        
        # Mark messages as read
        self.mark_messages_as_read(messages)
        
        context['messages'] = messages
        context['can_send_files'] = self.can_send_files()
        
        return context
    
    def mark_messages_as_read(self, messages):
        user_role = self.request.user.role
        
        if user_role == User.RoleChoices.CLIENT:
            messages.filter(read_by_client=False).update(
                read_by_client=True,
                read_at=timezone.now()
            )
            self.object.client_unread_count = 0
        else:
            messages.filter(read_by_talent=False).update(
                read_by_talent=True,
                read_at=timezone.now()
            )
            self.object.talent_unread_count = 0
        
        self.object.save()
```

## 📎 File Sharing System

### **File Upload View**
```python
class FileUploadView(DealParticipantMixin, View):
    def post(self, request, deal_id):
        deal = get_object_or_404(Deal, id=deal_id)
        
        if 'file' not in request.FILES:
            return JsonResponse({'error': 'No file provided'}, status=400)
        
        file = request.FILES['file']
        
        # Validate file
        if not self.validate_file(file):
            return JsonResponse({'error': 'Invalid file'}, status=400)
        
        # Create message with attachment
        message = Message.objects.create(
            deal=deal,
            sender=request.user,
            content=f"Shared file: {file.name}",
            message_type=Message.MessageType.FILE,
            attachment=file,
            attachment_name=file.name,
            attachment_size=file.size
        )
        
        # Send real-time notification
        channel_layer = get_channel_layer()
        async_to_sync(channel_layer.group_send)(
            f'chat_{deal.id}',
            {
                'type': 'chat_message',
                'message': self.serialize_message(message)
            }
        )
        
        return JsonResponse({
            'success': True,
            'message_id': message.id,
            'file_url': message.attachment.url
        })
    
    def validate_file(self, file):
        # Check file size (10MB max)
        if file.size > 10 * 1024 * 1024:
            return False
        
        # Check file type
        allowed_types = [
            'image/jpeg', 'image/png', 'image/gif',
            'application/pdf', 'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain', 'application/zip'
        ]
        
        return file.content_type in allowed_types
```

## 🔔 Notification Integration

### **Message Notifications**
```python
from django.db.models.signals import post_save
from django.dispatch import receiver

@receiver(post_save, sender=Message)
def send_message_notification(sender, instance, created, **kwargs):
    if not created:
        return
    
    message = instance
    deal = message.deal
    
    # Determine recipient
    if message.sender.role == User.RoleChoices.CLIENT:
        recipient = deal.talent.user
        unread_count = deal.talent_unread_count
    else:
        recipient = deal.client.user
        unread_count = deal.client_unread_count
    
    # Send email notification if user is offline
    if not is_user_online(recipient):
        send_email_notification.delay(
            recipient.email,
            f"New message from {message.sender.get_full_name()}",
            message.content[:100]
        )
    
    # Send push notification
    send_push_notification.delay(
        recipient.id,
        f"New message in {deal.job.title}",
        message.content[:50],
        {'deal_id': deal.id, 'unread_count': unread_count}
    )
```

## 📊 Chat Analytics

### **Message Statistics**
```python
class ChatAnalyticsView(DealParticipantMixin, TemplateView):
    template_name = "chat/analytics.html"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        deal = self.get_deal()
        
        # Message counts
        total_messages = deal.messages.count()
        client_messages = deal.messages.filter(
            sender=deal.client.user
        ).count()
        talent_messages = deal.messages.filter(
            sender=deal.talent.user
        ).count()
        
        context.update({
            'total_messages': total_messages,
            'client_messages': client_messages,
            'talent_messages': talent_messages,
            'response_time': self.calculate_avg_response_time(deal),
            'most_active_day': self.get_most_active_day(deal),
            'file_shares': deal.messages.filter(
                message_type=Message.MessageType.FILE
            ).count()
        })
        
        return context
    
    def calculate_avg_response_time(self, deal):
        messages = deal.messages.order_by('created_at')
        response_times = []
        
        for i in range(1, len(messages)):
            prev_msg = messages[i-1]
            curr_msg = messages[i]
            
            if prev_msg.sender != curr_msg.sender:
                time_diff = curr_msg.created_at - prev_msg.created_at
                response_times.append(time_diff.total_seconds())
        
        if response_times:
            avg_seconds = sum(response_times) / len(response_times)
            return timedelta(seconds=avg_seconds)
        
        return None
```

## 🎯 Key Takeaways

1. **Real-time Communication**: WebSocket-based instant messaging
2. **File Sharing**: Secure file upload with validation
3. **Message Status**: Read receipts and delivery tracking
4. **Notification System**: Email and push notifications
5. **Analytics**: Communication patterns and response times

## 🔗 What's Next?

**Next**: [Lesson 19: Payment & Finance Integration](./19-payment-system.md)
