{% load crispy_forms_tags %}

<turbo-frame id="profile_basic">
    <form method="post" action="{% url "clients:basic_change" %}" novalidate>
        {% csrf_token %}
        {{ form|crispy }}
        <div class="flex flex-wrap justify-start gap-3">
            <button type="submit" class="w-full sm:w-44 px-5 py-2.5 sm:py-3.5 text-sm font-medium text-center text-white rounded-[100px] border border-downy-300 bg-downy-300 hover:bg-purple-800 hover:border-purple-800 focus:ring-0">Save</button>
            <a href="{% url "clients:basic_show" %}" class="w-full sm:w-44 px-5 py-2.5 sm:py-3.5 text-sm font-medium text-center text-downy-300 rounded-[100px] bg-transparent border border-downy-300 hover:bg-purple-800 hover:border-purple-800 hover:text-white focus:ring-0 block">Cancel</a>
        </div>
    </form>
</turbo-frame>