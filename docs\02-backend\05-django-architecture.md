# Lesson 5: Django Architecture in Crelancer 🏛️

## 🎯 Learning Objectives
By the end of this lesson, you will understand:
- How Django's MVT pattern is implemented in Crelancer
- Custom user model and authentication system
- Model relationships and business logic
- View organization and patterns
- Template structure and inheritance

## 🏗️ Django MVT Architecture

### **Model-View-Template Pattern**
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Models    │◄──►│    Views    │◄──►│  Templates  │
│ (Database)  │    │ (Logic)     │    │   (HTML)    │
└─────────────┘    └─────────────┘    └─────────────┘
       ▲                   ▲                   ▲
       │                   │                   │
   PostgreSQL         Django Views         Hotwire
```

### **Request Flow in Crelancer**
```
1. Browser Request → URL Router → View
2. View → Model (Database Query)
3. Model → View (Data)
4. View → Template (Context)
5. Template → View (Rendered HTML)
6. View → Browser (HTTP Response)
```

## 👤 Custom User Model

### **User Model Architecture**
```python
# backend/apps/registration/models.py
class User(AbstractUser, UnRemovableModel):
    """
    Custom User model for authentication
    """
    USERNAME_FIELD = "email"  # Login with email instead of username
    REQUIRED_FIELDS = ["first_name", "last_name"]
    
    class RoleChoices(models.TextChoices):
        TALENT = "talent", "Talent"
        CLIENT = "client", "Client"
    
    username = None  # Remove username field
    email = LowercaseEmailField(unique=True)
    role = models.CharField(max_length=10, choices=RoleChoices.choices)
    
    # Soft delete functionality from UnRemovableModel
    is_removed = models.BooleanField(default=False)
```

### **Profile Models (One-to-One Relationship)**
```python
# Talent Profile
class Talent(models.Model):
    user = models.OneToOneField("registration.User", on_delete=models.CASCADE)
    is_verified = models.BooleanField(default=False)
    is_published = models.BooleanField(default=False)
    professional_title = models.CharField(max_length=255, null=True)
    about = models.TextField(null=True, blank=True)
    rate_hourly = models.DecimalField(max_digits=10, decimal_places=2)
    stripe_account_id = models.CharField(max_length=255, null=True)
    
    # Denormalized fields for performance
    reviews_rating = models.DecimalField(max_digits=3, decimal_places=2, default=0)
    reviews_count = models.PositiveIntegerField(default=0)

# Client Profile  
class Client(models.Model):
    user = models.OneToOneField("registration.User", on_delete=models.CASCADE)
    about = models.TextField(null=True, blank=True)
    website = models.URLField(null=True, blank=True)
    stripe_customer_id = models.CharField(max_length=255, null=True)
    
    # Denormalized fields for performance
    reviews_rating = models.DecimalField(max_digits=3, decimal_places=2, default=0)
    reviews_count = models.PositiveIntegerField(default=0)
```

## 🔗 Model Relationships

### **Core Business Models**
```python
# Job Model
class Job(models.Model):
    client = models.ForeignKey("clients.Client", on_delete=models.CASCADE)
    title = models.CharField(max_length=255)
    description = models.TextField()
    category = models.ForeignKey("taxonomy.Category", on_delete=models.CASCADE)
    skills = models.ManyToManyField("taxonomy.Skill")
    budget_min = models.DecimalField(max_digits=10, decimal_places=2)
    budget_max = models.DecimalField(max_digits=10, decimal_places=2)
    status = models.CharField(max_length=20, choices=StatusChoices.choices)

# Deal Model (Contract between Client and Talent)
class Deal(models.Model):
    job = models.ForeignKey("jobs.Job", on_delete=models.CASCADE)
    talent = models.ForeignKey("talents.Talent", on_delete=models.CASCADE)
    client = models.ForeignKey("clients.Client", on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=StatusChoices.choices)
    
    # Chat functionality
    last_message_at = models.DateTimeField(auto_now_add=True)
    client_unread_count = models.PositiveIntegerField(default=0)
    talent_unread_count = models.PositiveIntegerField(default=0)
    
    class Meta:
        unique_together = [["job", "talent"]]  # One deal per job-talent pair
```

### **Relationship Patterns**

#### **One-to-One**: User Profiles
```python
# Each user has exactly one profile based on their role
user.talent  # Access talent profile
user.client  # Access client profile
```

#### **One-to-Many**: Ownership
```python
# One client can have many jobs
client.job_set.all()

# One talent can have many deals
talent.deal_set.all()
```

#### **Many-to-Many**: Skills and Categories
```python
# Jobs can have multiple skills
job.skills.all()

# Talents can have multiple skills (with ordering)
talent.skills.all()
```

## 🎯 View Architecture

### **View Organization Pattern**
```
app/views/
├── 📄 __init__.py
├── 📄 common.py        # Shared views
├── 📄 talent.py        # Talent-specific views
├── 📄 client.py        # Client-specific views
└── 📄 api.py           # API views
```

### **View Types and Patterns**

#### **Class-Based Views (CBVs)**
```python
# List View Example
class TalentListView(ListView):
    model = Talent
    template_name = "talents/talent_list.html"
    context_object_name = "talents"
    paginate_by = 20
    
    def get_queryset(self):
        return Talent.objects.filter(
            is_published=True,
            is_verified=True
        ).select_related('user')

# Detail View Example  
class JobDetailView(DetailView):
    model = Job
    template_name = "jobs/job_detail.html"
    context_object_name = "job"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['related_jobs'] = Job.objects.filter(
            category=self.object.category
        ).exclude(pk=self.object.pk)[:5]
        return context
```

#### **Form Views**
```python
class JobCreateView(CreateView):
    model = Job
    form_class = JobForm
    template_name = "jobs/job_form.html"
    
    def form_valid(self, form):
        form.instance.client = self.request.user.client
        return super().form_valid(form)
    
    def get_success_url(self):
        return reverse('jobs:detail', kwargs={'pk': self.object.pk})
```

#### **Custom Mixins**
```python
# Permission Mixins
class TalentRequiredMixin(UserPassesTestMixin):
    def test_func(self):
        return (
            self.request.user.is_authenticated and 
            self.request.user.role == User.RoleChoices.TALENT
        )

class ClientRequiredMixin(UserPassesTestMixin):
    def test_func(self):
        return (
            self.request.user.is_authenticated and 
            self.request.user.role == User.RoleChoices.CLIENT
        )

# Usage
class TalentProfileView(TalentRequiredMixin, UpdateView):
    model = Talent
    # ...
```

### **Hotwire Integration**
```python
# Turbo Frame Views
class JobApplicationsFrameView(TemplateView):
    template_name = "jobs/applications_frame.html"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        job = get_object_or_404(Job, pk=kwargs['job_id'])
        context['applications'] = Deal.objects.filter(
            job=job,
            status=Deal.StatusChoices.PENDING
        )
        return context

# Turbo Stream Responses
from turbo_response import TurboStream

def create_application(request, job_id):
    # Create application logic
    deal = Deal.objects.create(...)
    
    return TurboStream("applications").append.response(
        "jobs/application_item.html",
        {"application": deal}
    )
```

## 📄 Template Architecture

### **Template Inheritance**
```html
<!-- base.html -->
<!DOCTYPE html>
<html>
<head>
    <title>{% block title %}Crelancer{% endblock %}</title>
    {% load webpack_loader %}
    {% render_bundle 'app' 'css' %}
</head>
<body>
    <nav>{% include 'components/navbar.html' %}</nav>
    
    <main>
        {% block content %}{% endblock %}
    </main>
    
    {% render_bundle 'app' 'js' %}
</body>
</html>

<!-- layouts/app.html -->
{% extends 'base.html' %}

{% block content %}
<div class="container mx-auto px-4">
    {% block app_content %}{% endblock %}
</div>
{% endblock %}

<!-- jobs/job_detail.html -->
{% extends 'layouts/app.html' %}

{% block title %}{{ job.title }} - Crelancer{% endblock %}

{% block app_content %}
<div class="job-detail">
    <h1>{{ job.title }}</h1>
    <p>{{ job.description }}</p>
</div>
{% endblock %}
```

### **Component-Based Templates**
```html
<!-- components/job_card.html -->
<div class="bg-white rounded-lg shadow-md p-6">
    <h3 class="text-xl font-semibold mb-2">{{ job.title }}</h3>
    <p class="text-gray-600 mb-4">{{ job.description|truncatewords:20 }}</p>
    <div class="flex justify-between items-center">
        <span class="text-green-600 font-bold">${{ job.budget_min }} - ${{ job.budget_max }}</span>
        <a href="{% url 'jobs:detail' job.pk %}" class="btn btn-primary">View Details</a>
    </div>
</div>

<!-- Usage in job_list.html -->
{% for job in jobs %}
    {% include 'components/job_card.html' %}
{% endfor %}
```

### **Turbo Frame Templates**
```html
<!-- jobs/applications_frame.html -->
<turbo-frame id="job-applications">
    <div class="applications-list">
        {% for application in applications %}
            <div class="application-item">
                <h4>{{ application.talent.user.get_full_name }}</h4>
                <p>{{ application.proposal }}</p>
                <div class="actions">
                    <button data-action="click->hiring#accept" 
                            data-hiring-deal-id="{{ application.id }}">
                        Accept
                    </button>
                </div>
            </div>
        {% endfor %}
    </div>
</turbo-frame>
```

## 🔧 Custom Managers and QuerySets

### **Custom Managers**
```python
class TalentManager(models.Manager):
    def published(self):
        return self.filter(is_published=True)
    
    def verified(self):
        return self.filter(is_verified=True)
    
    def available(self):
        return self.published().verified()

class Talent(models.Model):
    # ... fields ...
    
    objects = TalentManager()
    
    # Usage: Talent.objects.available()
```

### **Custom QuerySets**
```python
class JobQuerySet(models.QuerySet):
    def published(self):
        return self.filter(status=Job.StatusChoices.PUBLISHED)
    
    def in_category(self, category):
        return self.filter(category=category)
    
    def with_skills(self, skills):
        return self.filter(skills__in=skills).distinct()
    
    def budget_range(self, min_budget, max_budget):
        return self.filter(
            budget_min__gte=min_budget,
            budget_max__lte=max_budget
        )

class Job(models.Model):
    # ... fields ...
    
    objects = JobQuerySet.as_manager()
    
    # Usage: Job.objects.published().in_category(category).with_skills(skills)
```

## 🔐 Authentication and Permissions

### **Custom Authentication Backend**
```python
# Custom authentication to use email instead of username
class EmailBackend(ModelBackend):
    def authenticate(self, request, username=None, password=None, **kwargs):
        try:
            user = User.objects.get(email=username)
            if user.check_password(password):
                return user
        except User.DoesNotExist:
            return None
```

### **Permission Decorators**
```python
from functools import wraps
from django.core.exceptions import PermissionDenied

def talent_required(view_func):
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('login')
        if request.user.role != User.RoleChoices.TALENT:
            raise PermissionDenied
        return view_func(request, *args, **kwargs)
    return _wrapped_view

# Usage
@talent_required
def talent_dashboard(request):
    # View logic
    pass
```

## 📊 Database Optimization

### **Query Optimization**
```python
# Use select_related for foreign keys
talents = Talent.objects.select_related('user').filter(is_published=True)

# Use prefetch_related for many-to-many
jobs = Job.objects.prefetch_related('skills', 'category').all()

# Optimize with annotations
from django.db.models import Count, Avg

talents_with_stats = Talent.objects.annotate(
    job_count=Count('deal'),
    avg_rating=Avg('reviews__rating')
)
```

### **Database Indexes**
```python
class Deal(models.Model):
    # ... fields ...
    
    class Meta:
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['last_message_at']),
            models.Index(fields=['job', 'talent']),
        ]
```

## 🎯 Key Takeaways

1. **Custom User Model**: Email-based authentication with role-based profiles
2. **Clear Model Relationships**: One-to-One for profiles, Foreign Keys for ownership
3. **Organized Views**: Separated by functionality and user type
4. **Template Inheritance**: Consistent layout with reusable components
5. **Hotwire Integration**: Turbo Frames and Streams for dynamic updates
6. **Performance Optimization**: Custom managers, querysets, and database indexes

## 🔗 What's Next?

Now that you understand the Django architecture, let's dive deep into the models and database design to see how the business logic is implemented.

**Next**: [Lesson 6: Models & Database Design](./06-models-database.md)

---

## 💡 Quick Quiz

1. What field is used for authentication instead of username?
2. How are user profiles organized for different roles?
3. What's the purpose of the Deal model?
4. How are Turbo Frames integrated with Django views?

*Answers: 1) email, 2) One-to-One relationships with User model, 3) Contracts between clients and talents, 4) Special views return Turbo Frame templates*
