# Exercise 4: Analytics Dashboard 📊

## 🎯 Objective

Create a comprehensive analytics dashboard with interactive charts, key performance indicators (KPIs), and business intelligence features. This exercise focuses on data visualization, performance optimization, and creating actionable insights for platform administrators and users.

## 📋 Prerequisites

- Completed Lessons 1-25 (Full tutorial through Performance Optimization)
- Understanding of data aggregation and complex queries
- Knowledge of JavaScript charting libraries
- Familiarity with caching strategies

## 🎨 What You'll Build

### **Admin Analytics Dashboard**
- Platform-wide KPIs and metrics
- User growth and engagement analytics
- Revenue and transaction analytics
- Job posting and completion trends
- Interactive charts and visualizations

### **User Analytics (Talent/Client)**
- Personal performance metrics
- Earnings/spending analytics
- Project success rates
- Market insights and trends

## 🛠️ Implementation Steps

### **Step 1: Analytics Data Models and Services**

```python
# backend/apps/analytics/models.py
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, timedelta
import uuid

User = get_user_model()

class AnalyticsEvent(models.Model):
    class EventType(models.TextChoices):
        PAGE_VIEW = 'page_view', 'Page View'
        USER_REGISTRATION = 'user_registration', 'User Registration'
        JOB_POSTED = 'job_posted', 'Job Posted'
        JOB_APPLICATION = 'job_application', 'Job Application'
        DEAL_CREATED = 'deal_created', 'Deal Created'
        PAYMENT_COMPLETED = 'payment_completed', 'Payment Completed'
        MESSAGE_SENT = 'message_sent', 'Message Sent'
        PROFILE_UPDATED = 'profile_updated', 'Profile Updated'
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    event_type = models.CharField(max_length=50, choices=EventType.choices)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    
    # Event data
    properties = models.JSONField(default=dict)
    session_id = models.CharField(max_length=255, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    
    # Timestamps
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['event_type', 'timestamp']),
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['timestamp']),
        ]

class DailyMetrics(models.Model):
    """Aggregated daily metrics for performance"""
    date = models.DateField(unique=True)
    
    # User metrics
    new_users = models.PositiveIntegerField(default=0)
    active_users = models.PositiveIntegerField(default=0)
    returning_users = models.PositiveIntegerField(default=0)
    
    # Job metrics
    jobs_posted = models.PositiveIntegerField(default=0)
    applications_submitted = models.PositiveIntegerField(default=0)
    deals_created = models.PositiveIntegerField(default=0)
    deals_completed = models.PositiveIntegerField(default=0)
    
    # Financial metrics
    total_revenue = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_volume = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    avg_deal_value = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    
    # Engagement metrics
    messages_sent = models.PositiveIntegerField(default=0)
    page_views = models.PositiveIntegerField(default=0)
    session_duration_avg = models.DurationField(null=True, blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-date']

# backend/apps/analytics/services.py
from django.db.models import Count, Sum, Avg, Q, F
from django.utils import timezone
from datetime import datetime, timedelta
from django.core.cache import cache
from jobs.models import Job, Deal
from finance.models import Transaction
from chat.models import Message
from .models import AnalyticsEvent, DailyMetrics

class AnalyticsService:
    @staticmethod
    def track_event(event_type, user=None, properties=None, request=None):
        """Track an analytics event"""
        event_data = {
            'event_type': event_type,
            'user': user,
            'properties': properties or {},
        }
        
        if request:
            event_data.update({
                'session_id': request.session.session_key,
                'ip_address': AnalyticsService.get_client_ip(request),
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            })
        
        return AnalyticsEvent.objects.create(**event_data)
    
    @staticmethod
    def get_client_ip(request):
        """Get client IP address from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    @staticmethod
    def get_platform_overview(days=30):
        """Get platform overview metrics"""
        cache_key = f'platform_overview_{days}'
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        # User metrics
        total_users = User.objects.count()
        new_users = User.objects.filter(
            date_joined__date__gte=start_date
        ).count()
        
        active_users = User.objects.filter(
            last_login__date__gte=start_date
        ).count()
        
        # Job metrics
        total_jobs = Job.objects.count()
        active_jobs = Job.objects.filter(status='published').count()
        completed_deals = Deal.objects.filter(
            status='completed',
            updated_at__date__gte=start_date
        ).count()
        
        # Financial metrics
        revenue_data = Transaction.objects.filter(
            transaction_type='fee',
            status='completed',
            created_at__date__gte=start_date
        ).aggregate(
            total_revenue=Sum('amount'),
            transaction_count=Count('id')
        )
        
        volume_data = Transaction.objects.filter(
            transaction_type='payment',
            status='completed',
            created_at__date__gte=start_date
        ).aggregate(
            total_volume=Sum('amount'),
            avg_deal_value=Avg('amount')
        )
        
        overview = {
            'users': {
                'total': total_users,
                'new': new_users,
                'active': active_users,
                'growth_rate': (new_users / max(total_users - new_users, 1)) * 100
            },
            'jobs': {
                'total': total_jobs,
                'active': active_jobs,
                'completed_deals': completed_deals,
                'completion_rate': (completed_deals / max(total_jobs, 1)) * 100
            },
            'revenue': {
                'total': revenue_data['total_revenue'] or 0,
                'transactions': revenue_data['transaction_count'] or 0,
                'volume': volume_data['total_volume'] or 0,
                'avg_deal_value': volume_data['avg_deal_value'] or 0
            }
        }
        
        cache.set(cache_key, overview, 3600)  # Cache for 1 hour
        return overview
    
    @staticmethod
    def get_user_growth_data(days=90):
        """Get user growth data for charts"""
        cache_key = f'user_growth_{days}'
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        # Daily new users
        daily_users = User.objects.filter(
            date_joined__date__gte=start_date
        ).extra(
            select={'day': 'date(date_joined)'}
        ).values('day').annotate(
            count=Count('id')
        ).order_by('day')
        
        # Cumulative users
        cumulative_data = []
        total = User.objects.filter(date_joined__date__lt=start_date).count()
        
        for entry in daily_users:
            total += entry['count']
            cumulative_data.append({
                'date': entry['day'],
                'cumulative': total,
                'daily': entry['count']
            })
        
        growth_data = {
            'daily': list(daily_users),
            'cumulative': cumulative_data
        }
        
        cache.set(cache_key, growth_data, 1800)  # Cache for 30 minutes
        return growth_data
    
    @staticmethod
    def get_revenue_analytics(days=90):
        """Get revenue analytics data"""
        cache_key = f'revenue_analytics_{days}'
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return cached_data
        
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=days)
        
        # Daily revenue
        daily_revenue = Transaction.objects.filter(
            transaction_type='fee',
            status='completed',
            created_at__date__gte=start_date
        ).extra(
            select={'day': 'date(created_at)'}
        ).values('day').annotate(
            revenue=Sum('amount'),
            transactions=Count('id')
        ).order_by('day')
        
        # Revenue by category
        category_revenue = Transaction.objects.filter(
            transaction_type='fee',
            status='completed',
            created_at__date__gte=start_date
        ).values(
            'deal__job__category__name'
        ).annotate(
            revenue=Sum('amount'),
            deals=Count('deal', distinct=True)
        ).order_by('-revenue')[:10]
        
        revenue_data = {
            'daily': list(daily_revenue),
            'by_category': list(category_revenue)
        }
        
        cache.set(cache_key, revenue_data, 1800)
        return revenue_data
```

### **Step 2: Analytics Views and API Endpoints**

```python
# backend/apps/analytics/views.py
from django.shortcuts import render
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.utils.decorators import method_decorator
from django.views.generic import TemplateView
from .services import AnalyticsService

@method_decorator(staff_member_required, name='dispatch')
class AdminAnalyticsDashboard(TemplateView):
    template_name = 'analytics/admin_dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get overview data
        context['overview'] = AnalyticsService.get_platform_overview()
        
        # Get recent metrics
        from .models import DailyMetrics
        context['recent_metrics'] = DailyMetrics.objects.all()[:30]
        
        return context

@login_required
def user_analytics_dashboard(request):
    """Analytics dashboard for individual users"""
    analytics_data = AnalyticsService.get_user_analytics(request.user)
    
    context = {
        'analytics': analytics_data,
        'user_role': request.user.role
    }
    
    return render(request, 'analytics/user_dashboard.html', context)

# API endpoints for AJAX requests
@staff_member_required
def api_platform_overview(request):
    """API endpoint for platform overview data"""
    days = int(request.GET.get('days', 30))
    data = AnalyticsService.get_platform_overview(days)
    return JsonResponse(data)

@staff_member_required
def api_user_growth(request):
    """API endpoint for user growth data"""
    days = int(request.GET.get('days', 90))
    data = AnalyticsService.get_user_growth_data(days)
    return JsonResponse(data)

@staff_member_required
def api_revenue_analytics(request):
    """API endpoint for revenue analytics"""
    days = int(request.GET.get('days', 90))
    data = AnalyticsService.get_revenue_analytics(days)
    return JsonResponse(data)

@login_required
def api_user_analytics(request):
    """API endpoint for user-specific analytics"""
    days = int(request.GET.get('days', 90))
    data = AnalyticsService.get_user_analytics(request.user, days)
    return JsonResponse(data)
```

### **Step 3: Interactive Dashboard Templates**

```html
<!-- backend/templates/analytics/admin_dashboard.html -->
{% extends 'admin/base.html' %}
{% load humanize %}

{% block title %}Analytics Dashboard{% endblock %}

{% block content %}
<div class="analytics-dashboard" 
     data-controller="analytics-dashboard"
     data-analytics-dashboard-overview-url-value="{% url 'analytics:api_platform_overview' %}"
     data-analytics-dashboard-user-growth-url-value="{% url 'analytics:api_user_growth' %}"
     data-analytics-dashboard-revenue-url-value="{% url 'analytics:api_revenue_analytics' %}">
    
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
        <p class="text-gray-600 mt-2">Platform performance and insights</p>
        
        <!-- Time Range Selector -->
        <div class="mt-4">
            <select data-analytics-dashboard-target="timeRange" 
                    data-action="change->analytics-dashboard#updateTimeRange"
                    class="rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                <option value="7">Last 7 days</option>
                <option value="30" selected>Last 30 days</option>
                <option value="90">Last 90 days</option>
                <option value="365">Last year</option>
            </select>
        </div>
    </div>

    <!-- KPI Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"/>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                        <dd class="text-lg font-medium text-gray-900" data-analytics-dashboard-target="totalUsers">
                            {{ overview.users.total|intcomma }}
                        </dd>
                    </dl>
                </div>
            </div>
            <div class="mt-4">
                <div class="flex items-center text-sm">
                    <span class="text-green-600 font-medium">+{{ overview.users.new|intcomma }}</span>
                    <span class="text-gray-500 ml-1">new this period</span>
                </div>
            </div>
        </div>
        <!-- Additional KPI cards... -->
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- User Growth Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">User Growth</h3>
                <div class="flex space-x-2">
                    <button type="button" 
                            class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded"
                            data-action="click->analytics-dashboard#showDailyGrowth">
                        Daily
                    </button>
                    <button type="button" 
                            class="px-3 py-1 text-sm bg-gray-100 text-gray-800 rounded"
                            data-action="click->analytics-dashboard#showCumulativeGrowth">
                        Cumulative
                    </button>
                </div>
            </div>
            <div class="h-64">
                <canvas data-analytics-dashboard-target="userGrowthChart"></canvas>
            </div>
        </div>

        <!-- Revenue Chart -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Revenue Trends</h3>
            </div>
            <div class="h-64">
                <canvas data-analytics-dashboard-target="revenueChart"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}
```

## ✅ Expected Outcome

After completing this exercise, you should have:

1. **Comprehensive Analytics Dashboard**: Interactive charts and KPIs for platform insights
2. **Real-time Data Visualization**: Dynamic charts that update based on time ranges
3. **User-specific Analytics**: Personalized dashboards for talents and clients
4. **Performance Optimization**: Efficient data aggregation and caching
5. **Business Intelligence**: Actionable insights for decision making

## 🚀 Extension Challenges

1. **Predictive Analytics**: Implement machine learning models for forecasting
2. **A/B Testing Framework**: Create system for testing feature variations
3. **Custom Report Builder**: Allow users to create custom analytics reports
4. **Real-time Dashboards**: Use WebSockets for live updating dashboards
5. **Export Functionality**: Add PDF/Excel export capabilities for reports

## 💡 Solution Tips

- Use database aggregation functions for efficient calculations
- Implement proper caching strategies for expensive queries
- Consider using background tasks for heavy analytics processing
- Use chart.js or similar libraries for interactive visualizations
- Implement proper error handling and loading states

Fantastic work! You've built a sophisticated analytics dashboard that provides valuable insights into platform performance and user behavior. You've now completed all four practical exercises! 🎉

**Congratulations on completing the Crelancer tutorial and all exercises!**
