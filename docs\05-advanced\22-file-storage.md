# Lesson 22: File Storage & Media Management 📁

## 🎯 Learning Objectives
- File upload and storage strategies
- AWS S3 integration for production
- Image processing and optimization
- Security and access control
- CDN integration for performance

## 📁 Storage Architecture

### **Storage Backend Configuration**
```python
# backend/core/storage_backends.py
from django.conf import settings
from storages.backends.s3boto3 import S3Boto3Storage

class MediaStorage(S3Boto3Storage):
    bucket_name = settings.AWS_STORAGE_BUCKET_NAME
    location = 'media'
    default_acl = 'private'
    file_overwrite = False
    custom_domain = False

class StaticStorage(S3Boto3Storage):
    bucket_name = settings.AWS_STORAGE_BUCKET_NAME
    location = 'static'
    default_acl = 'public-read'
    file_overwrite = True

# Settings configuration
if settings.USE_S3:
    DEFAULT_FILE_STORAGE = 'backend.core.storage_backends.MediaStorage'
    STATICFILES_STORAGE = 'backend.core.storage_backends.StaticStorage'
    AWS_S3_CUSTOM_DOMAIN = f'{AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com'
    AWS_S3_OBJECT_PARAMETERS = {
        'CacheControl': 'max-age=86400',
    }
else:
    MEDIA_URL = '/media/'
    MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
```

### **File Upload Models**
```python
class UploadedFile(models.Model):
    class FileType(models.TextChoices):
        IMAGE = 'image', 'Image'
        DOCUMENT = 'document', 'Document'
        PORTFOLIO = 'portfolio', 'Portfolio'
        VERIFICATION = 'verification', 'Verification'
        CHAT_ATTACHMENT = 'chat', 'Chat Attachment'
    
    uploader = models.ForeignKey(User, on_delete=models.CASCADE)
    file = models.FileField(upload_to='uploads/%Y/%m/')
    original_name = models.CharField(max_length=255)
    file_type = models.CharField(max_length=20, choices=FileType.choices)
    file_size = models.PositiveIntegerField()
    mime_type = models.CharField(max_length=100)
    
    # Security
    is_verified = models.BooleanField(default=False)
    virus_scan_status = models.CharField(max_length=20, default='pending')
    
    # Metadata
    uploaded_at = models.DateTimeField(auto_now_add=True)
    access_count = models.PositiveIntegerField(default=0)
    
    def get_secure_url(self, expires_in=3600):
        """Generate secure URL for private files"""
        if settings.USE_S3:
            return self.file.storage.url(self.file.name, expire=expires_in)
        return self.file.url
```

## 🖼️ Image Processing

### **Image Optimization Service**
```python
from PIL import Image, ImageOps
import io
from django.core.files.base import ContentFile

class ImageProcessingService:
    @staticmethod
    def process_image(image_file, max_width=1920, max_height=1080, quality=85):
        """Process and optimize uploaded images"""
        try:
            # Open image
            image = Image.open(image_file)
            
            # Convert to RGB if necessary
            if image.mode in ('RGBA', 'LA', 'P'):
                image = image.convert('RGB')
            
            # Auto-rotate based on EXIF data
            image = ImageOps.exif_transpose(image)
            
            # Resize if too large
            if image.width > max_width or image.height > max_height:
                image.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)
            
            # Save optimized image
            output = io.BytesIO()
            image.save(output, format='JPEG', quality=quality, optimize=True)
            output.seek(0)
            
            return ContentFile(output.read())
            
        except Exception as e:
            logger.error(f"Image processing failed: {e}")
            return image_file
    
    @staticmethod
    def create_thumbnail(image_file, size=(300, 300)):
        """Create thumbnail for image"""
        try:
            image = Image.open(image_file)
            
            # Convert to RGB if necessary
            if image.mode in ('RGBA', 'LA', 'P'):
                image = image.convert('RGB')
            
            # Create thumbnail
            image.thumbnail(size, Image.Resampling.LANCZOS)
            
            # Save thumbnail
            output = io.BytesIO()
            image.save(output, format='JPEG', quality=80, optimize=True)
            output.seek(0)
            
            return ContentFile(output.read())
            
        except Exception as e:
            logger.error(f"Thumbnail creation failed: {e}")
            return None
```

### **Portfolio Image Model**
```python
class PortfolioImage(models.Model):
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='portfolio/images/%Y/%m/')
    thumbnail = models.ImageField(upload_to='portfolio/thumbs/%Y/%m/', blank=True)
    
    title = models.CharField(max_length=255, blank=True)
    description = models.TextField(blank=True)
    sort_order = models.PositiveIntegerField(default=0)
    
    uploaded_at = models.DateTimeField(auto_now_add=True)
    
    def save(self, *args, **kwargs):
        if self.image and not self.thumbnail:
            # Process main image
            self.image = ImageProcessingService.process_image(self.image)
            
            # Create thumbnail
            thumbnail = ImageProcessingService.create_thumbnail(self.image)
            if thumbnail:
                self.thumbnail.save(
                    f"thumb_{self.image.name}",
                    thumbnail,
                    save=False
                )
        
        super().save(*args, **kwargs)
```

## 🔒 Security and Access Control

### **File Upload Validation**
```python
class FileUploadValidator:
    ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    ALLOWED_DOCUMENT_TYPES = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain'
    ]
    
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    MAX_IMAGE_SIZE = 5 * 1024 * 1024   # 5MB
    
    @classmethod
    def validate_file(cls, file, file_type):
        """Validate uploaded file"""
        errors = []
        
        # Check file size
        if file.size > cls.MAX_FILE_SIZE:
            errors.append(f"File size exceeds {cls.MAX_FILE_SIZE // (1024*1024)}MB limit")
        
        # Check file type
        if file_type == 'image':
            if file.content_type not in cls.ALLOWED_IMAGE_TYPES:
                errors.append("Invalid image format")
            if file.size > cls.MAX_IMAGE_SIZE:
                errors.append(f"Image size exceeds {cls.MAX_IMAGE_SIZE // (1024*1024)}MB limit")
        
        elif file_type == 'document':
            if file.content_type not in cls.ALLOWED_DOCUMENT_TYPES:
                errors.append("Invalid document format")
        
        # Check for malicious content
        if cls.has_malicious_content(file):
            errors.append("File contains potentially malicious content")
        
        return errors
    
    @classmethod
    def has_malicious_content(cls, file):
        """Basic malicious content detection"""
        # Check file extension vs content type mismatch
        file_ext = file.name.split('.')[-1].lower()
        
        dangerous_extensions = ['exe', 'bat', 'cmd', 'scr', 'pif', 'vbs', 'js']
        if file_ext in dangerous_extensions:
            return True
        
        # Check for embedded scripts in images
        if file.content_type.startswith('image/'):
            try:
                file.seek(0)
                content = file.read(1024)  # Read first 1KB
                file.seek(0)
                
                # Look for script tags or suspicious patterns
                suspicious_patterns = [b'<script', b'javascript:', b'<?php']
                for pattern in suspicious_patterns:
                    if pattern in content.lower():
                        return True
            except:
                pass
        
        return False
```

### **Secure File Serving**
```python
class SecureFileView(LoginRequiredMixin, View):
    def get(self, request, file_id):
        try:
            uploaded_file = UploadedFile.objects.get(id=file_id)
            
            # Check permissions
            if not self.has_file_access(request.user, uploaded_file):
                raise PermissionDenied
            
            # Increment access count
            uploaded_file.access_count += 1
            uploaded_file.save(update_fields=['access_count'])
            
            # Generate secure URL
            if settings.USE_S3:
                # Generate presigned URL for S3
                secure_url = uploaded_file.get_secure_url(expires_in=300)  # 5 minutes
                return redirect(secure_url)
            else:
                # Serve file directly for local storage
                response = FileResponse(
                    uploaded_file.file.open('rb'),
                    content_type=uploaded_file.mime_type
                )
                response['Content-Disposition'] = f'inline; filename="{uploaded_file.original_name}"'
                return response
                
        except UploadedFile.DoesNotExist:
            raise Http404
    
    def has_file_access(self, user, uploaded_file):
        """Check if user has access to file"""
        # Owner always has access
        if uploaded_file.uploader == user:
            return True
        
        # Check based on file type and context
        if uploaded_file.file_type == UploadedFile.FileType.PORTFOLIO:
            # Portfolio files are public if talent profile is published
            try:
                project = uploaded_file.portfolioimage_set.first().project
                return project.talent.is_published
            except:
                return False
        
        elif uploaded_file.file_type == UploadedFile.FileType.CHAT_ATTACHMENT:
            # Chat files accessible to deal participants
            try:
                message = uploaded_file.message_set.first()
                deal = message.deal
                return user in [deal.client.user, deal.talent.user]
            except:
                return False
        
        return False
```

## 🚀 CDN Integration

### **CloudFront Configuration**
```python
# settings/production.py
AWS_S3_CUSTOM_DOMAIN = 'cdn.crelancer.com'
AWS_S3_OBJECT_PARAMETERS = {
    'CacheControl': 'max-age=86400',  # 24 hours
}

# For static files
STATICFILES_STORAGE = 'backend.core.storage_backends.StaticStorage'
STATIC_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/static/'

# For media files with different cache settings
class MediaCDNStorage(S3Boto3Storage):
    bucket_name = settings.AWS_STORAGE_BUCKET_NAME
    location = 'media'
    default_acl = 'private'
    custom_domain = settings.AWS_S3_CUSTOM_DOMAIN
    
    def get_object_parameters(self, name):
        params = super().get_object_parameters(name)
        
        # Different cache settings based on file type
        if name.endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp')):
            params['CacheControl'] = 'max-age=2592000'  # 30 days for images
        elif name.endswith(('.pdf', '.doc', '.docx')):
            params['CacheControl'] = 'max-age=86400'    # 1 day for documents
        else:
            params['CacheControl'] = 'max-age=3600'     # 1 hour for others
        
        return params
```

## 📊 File Management Analytics

### **Storage Analytics**
```python
class FileAnalyticsView(UserPassesTestMixin, TemplateView):
    template_name = "admin/file_analytics.html"
    
    def test_func(self):
        return self.request.user.is_staff
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Storage usage by type
        storage_by_type = UploadedFile.objects.values('file_type').annotate(
            total_size=Sum('file_size'),
            file_count=Count('id')
        )
        
        # Top uploaders
        top_uploaders = UploadedFile.objects.values(
            'uploader__email'
        ).annotate(
            total_size=Sum('file_size'),
            file_count=Count('id')
        ).order_by('-total_size')[:10]
        
        # Monthly upload trends
        monthly_uploads = UploadedFile.objects.extra(
            select={'month': "DATE_FORMAT(uploaded_at, '%%Y-%%m')"}
        ).values('month').annotate(
            total_size=Sum('file_size'),
            file_count=Count('id')
        ).order_by('month')
        
        context.update({
            'storage_by_type': storage_by_type,
            'top_uploaders': top_uploaders,
            'monthly_uploads': monthly_uploads,
            'total_storage': sum(item['total_size'] for item in storage_by_type),
            'total_files': UploadedFile.objects.count()
        })
        
        return context
```

## 🎯 Key Takeaways

1. **Flexible Storage**: Support both local and S3 storage backends
2. **Image Processing**: Automatic optimization and thumbnail generation
3. **Security**: File validation and access control
4. **Performance**: CDN integration for fast delivery
5. **Analytics**: Monitor storage usage and trends

## 🔗 What's Next?

**Next**: [Lesson 23: Notifications & Email System](./23-notifications.md)
