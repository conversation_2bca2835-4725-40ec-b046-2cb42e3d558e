# Generated by Django 3.2.18 on 2024-10-21 13:12

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('deals', '0005_alter_deal_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserDealSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_archived', models.BooleanField(default=False)),
                ('is_deleted', models.BooleanField(default=False)),
                ('deal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='deals.deal')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'deal')},
            },
        ),
        migrations.AddField(
            model_name='deal',
            name='user_deal_settings',
            field=models.ManyToManyField(through='deals.UserDealSettings', to=settings.AUTH_USER_MODEL),
        ),
    ]
