{% if user.is_client %}
    {% if user.client.has_payment_method %}
        <a
                href="{% url 'deals:job_invitation' talent.id %}"
                class="block w-full sm:w-44  px-5 py-2.5 sm:py-3.5 text-sm font-medium text-center text-white rounded-[100px] border border-downy-300 bg-downy-300 hover:bg-purple-800 hover:border-purple-800 focus:ring-0 mt-3 sm:mt-0">
                Hire
        </a>
        {% if last_deal_id %}
            <a
                    href="{% url 'chat:message_list' deal_id=last_deal_id %}"
                    class="inline-flex items-center w-48 px-5 py-2.5 sm:py-3.5 text-sm font-medium text-center text-white rounded-[100px] border border-downy-300 bg-downy-300 hover:bg-purple-800 hover:border-purple-800 focus:ring-0">
                    <svg width="15" height="14" viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none" class="mr-2">
                        <path d="M0.833332 3.66675L6.27661 7.47704C6.71739 7.78559 6.93778 7.93986 7.17751 7.99962C7.38926 8.0524 7.61074 8.0524 7.82249 7.99962C8.06221 7.93986 8.28261 7.78559 8.72339 7.47704L14.1667 3.66675M4.03333 12.3334H10.9667C12.0868 12.3334 12.6468 12.3334 13.0746 12.1154C13.451 11.9237 13.7569 11.6177 13.9487 11.2414C14.1667 10.8136 14.1667 10.2535 14.1667 9.13341V4.86675C14.1667 3.74664 14.1667 3.18659 13.9487 2.75877C13.7569 2.38244 13.451 2.07648 13.0746 1.88473C12.6468 1.66675 12.0868 1.66675 10.9667 1.66675H4.03333C2.91323 1.66675 2.35317 1.66675 1.92535 1.88473C1.54903 2.07648 1.24307 2.38244 1.05132 2.75877C0.833332 3.18659 0.833332 3.74664 0.833332 4.86675V9.13341C0.833332 10.2535 0.833332 10.8136 1.05132 11.2414C1.24307 11.6177 1.54903 11.9237 1.92535 12.1154C2.35317 12.3334 2.91323 12.3334 4.03333 12.3334Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      </svg>
                    Go to messenger
            </a>
        {% endif %}
    {% else %}
         <button data-tooltip-target="tooltip-hover" data-tooltip-trigger="hover" type="button"
                class="inline-flex items-center w-48 px-5 py-2.5 sm:py-3.5 text-sm font-medium text-center text-gray-500 rounded-[100px] border border-gray-300 bg-gray-300 cursor-not-allowed">
                <svg width="15" height="14" viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none" class="mr-2">
                    <path d="M0.833332 3.66675L6.27661 7.47704C6.71739 7.78559 6.93778 7.93986 7.17751 7.99962C7.38926 8.0524 7.61074 8.0524 7.82249 7.99962C8.06221 7.93986 8.28261 7.78559 8.72339 7.47704L14.1667 3.66675M4.03333 12.3334H10.9667C12.0868 12.3334 12.6468 12.3334 13.0746 12.1154C13.451 11.9237 13.7569 11.6177 13.9487 11.2414C14.1667 10.8136 14.1667 10.2535 14.1667 9.13341V4.86675C14.1667 3.74664 14.1667 3.18659 13.9487 2.75877C13.7569 2.38244 13.451 2.07648 13.0746 1.88473C12.6468 1.66675 12.0868 1.66675 10.9667 1.66675H4.03333C2.91323 1.66675 2.35317 1.66675 1.92535 1.88473C1.54903 2.07648 1.24307 2.38244 1.05132 2.75877C0.833332 3.18659 0.833332 3.74664 0.833332 4.86675V9.13341C0.833332 10.2535 0.833332 10.8136 1.05132 11.2414C1.24307 11.6177 1.54903 11.9237 1.92535 12.1154C2.35317 12.3334 2.91323 12.3334 4.03333 12.3334Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                  </svg>
                Go to messenger
        </button>
        <div id="tooltip-hover" role="tooltip" class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-red-700 bg-white rounded-lg shadow-sm opacity-0 tooltip shadow-md">
            <div class="inline-flex items-center font-medium">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="flex-shrink-0 inline w-5 h-5 mr-1">
                    <path fill-rule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zm-1.72 6.97a.75.75 0 10-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 101.06 1.06L12 13.06l1.72 1.72a.75.75 0 101.06-1.06L13.06 12l1.72-1.72a.75.75 0 10-1.06-1.06L12 10.94l-1.72-1.72z" clip-rule="evenodd"></path>
                </svg>
                Add Payment Details!
            </div>            
            <div class="tooltip-arrow" data-popper-arrow></div>
        </div>
    {% endif %}
{% endif %}