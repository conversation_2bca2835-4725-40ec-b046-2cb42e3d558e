# Lesson 19: Payment & Finance Integration 💳

## 🎯 Learning Objectives
- Stripe integration architecture
- Payment processing workflow
- Marketplace payments and payouts
- Transaction tracking and reconciliation
- Financial reporting and analytics

## 💰 Payment Model Structure

```python
class Transaction(models.Model):
    class TransactionType(models.TextChoices):
        PAYMENT = "payment", "Payment"
        PAYOUT = "payout", "Payout"
        REFUND = "refund", "Refund"
        FEE = "fee", "Platform Fee"
        CHARGEBACK = "chargeback", "Chargeback"
    
    class Status(models.TextChoices):
        PENDING = "pending", "Pending"
        PROCESSING = "processing", "Processing"
        COMPLETED = "completed", "Completed"
        FAILED = "failed", "Failed"
        CANCELLED = "cancelled", "Cancelled"
    
    # Relationships
    deal = models.ForeignKey(Deal, on_delete=models.CASCADE)
    milestone = models.ForeignKey(Milestone, on_delete=models.CASCADE, null=True)
    
    # Transaction details
    transaction_type = models.CharField(max_length=20, choices=TransactionType.choices)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    fee_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    net_amount = models.DecimalField(max_digits=10, decimal_places=2)
    currency = models.CharField(max_length=3, default='USD')
    
    # Status and metadata
    status = models.CharField(max_length=20, choices=Status.choices)
    description = models.TextField(blank=True)
    
    # Stripe integration
    stripe_payment_intent_id = models.CharField(max_length=255, null=True)
    stripe_transfer_id = models.CharField(max_length=255, null=True)
    stripe_charge_id = models.CharField(max_length=255, null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    processed_at = models.DateTimeField(null=True)
    completed_at = models.DateTimeField(null=True)
```

## 🔄 Payment Processing Workflow

### **Payment Intent Creation**
```python
import stripe
from django.conf import settings

stripe.api_key = settings.STRIPE_SECRET_KEY

class PaymentService:
    @staticmethod
    def create_payment_intent(milestone, client):
        """Create Stripe Payment Intent for milestone payment"""
        
        # Calculate amounts
        amount = milestone.amount
        platform_fee = amount * Decimal('0.05')  # 5% platform fee
        net_amount = amount - platform_fee
        
        try:
            # Create payment intent
            intent = stripe.PaymentIntent.create(
                amount=int(amount * 100),  # Convert to cents
                currency='usd',
                customer=client.stripe_customer_id,
                description=f"Payment for milestone: {milestone.title}",
                metadata={
                    'deal_id': milestone.deal.id,
                    'milestone_id': milestone.id,
                    'platform_fee': str(platform_fee)
                },
                # Hold funds for marketplace
                transfer_group=f"deal_{milestone.deal.id}"
            )
            
            # Create transaction record
            transaction = Transaction.objects.create(
                deal=milestone.deal,
                milestone=milestone,
                transaction_type=Transaction.TransactionType.PAYMENT,
                amount=amount,
                fee_amount=platform_fee,
                net_amount=net_amount,
                status=Transaction.Status.PENDING,
                stripe_payment_intent_id=intent.id,
                description=f"Payment for milestone: {milestone.title}"
            )
            
            return intent, transaction
            
        except stripe.error.StripeError as e:
            raise PaymentError(f"Failed to create payment intent: {str(e)}")
```

### **Payment Confirmation**
```python
class PaymentConfirmView(ClientRequiredMixin, View):
    def post(self, request):
        payment_intent_id = request.POST.get('payment_intent_id')
        
        try:
            # Retrieve payment intent from Stripe
            intent = stripe.PaymentIntent.retrieve(payment_intent_id)
            
            if intent.status == 'succeeded':
                # Find transaction
                transaction = Transaction.objects.get(
                    stripe_payment_intent_id=payment_intent_id
                )
                
                # Update transaction status
                transaction.status = Transaction.Status.COMPLETED
                transaction.processed_at = timezone.now()
                transaction.completed_at = timezone.now()
                transaction.save()
                
                # Update milestone status
                milestone = transaction.milestone
                milestone.status = Milestone.StatusChoices.PAID
                milestone.save()
                
                # Process payout to talent
                self.process_talent_payout(transaction)
                
                # Send notifications
                send_payment_confirmation.delay(transaction.id)
                
                return JsonResponse({
                    'success': True,
                    'message': 'Payment processed successfully'
                })
            
        except (Transaction.DoesNotExist, stripe.error.StripeError) as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=400)
    
    def process_talent_payout(self, payment_transaction):
        """Process payout to talent's Stripe Connect account"""
        
        talent = payment_transaction.deal.talent
        
        if not talent.stripe_account_id:
            raise PaymentError("Talent doesn't have connected Stripe account")
        
        try:
            # Create transfer to talent
            transfer = stripe.Transfer.create(
                amount=int(payment_transaction.net_amount * 100),
                currency='usd',
                destination=talent.stripe_account_id,
                transfer_group=f"deal_{payment_transaction.deal.id}",
                description=f"Payout for milestone: {payment_transaction.milestone.title}"
            )
            
            # Create payout transaction record
            Transaction.objects.create(
                deal=payment_transaction.deal,
                milestone=payment_transaction.milestone,
                transaction_type=Transaction.TransactionType.PAYOUT,
                amount=payment_transaction.net_amount,
                fee_amount=0,
                net_amount=payment_transaction.net_amount,
                status=Transaction.Status.COMPLETED,
                stripe_transfer_id=transfer.id,
                processed_at=timezone.now(),
                completed_at=timezone.now(),
                description=f"Payout for milestone: {payment_transaction.milestone.title}"
            )
            
        except stripe.error.StripeError as e:
            raise PaymentError(f"Failed to process payout: {str(e)}")
```

## 🔗 Stripe Connect Integration

### **Talent Account Setup**
```python
class StripeConnectView(TalentRequiredMixin, View):
    def get(self, request):
        talent = request.user.talent
        
        if not talent.stripe_account_id:
            # Create Stripe Connect account
            account = stripe.Account.create(
                type='express',
                country='US',
                email=request.user.email,
                capabilities={
                    'card_payments': {'requested': True},
                    'transfers': {'requested': True},
                },
                business_type='individual',
                individual={
                    'first_name': request.user.first_name,
                    'last_name': request.user.last_name,
                    'email': request.user.email,
                }
            )
            
            talent.stripe_account_id = account.id
            talent.save()
        
        # Create account link for onboarding
        account_link = stripe.AccountLink.create(
            account=talent.stripe_account_id,
            refresh_url=request.build_absolute_uri(
                reverse('talents:stripe_connect')
            ),
            return_url=request.build_absolute_uri(
                reverse('talents:stripe_connect_success')
            ),
            type='account_onboarding',
        )
        
        return redirect(account_link.url)

class StripeWebhookView(View):
    def post(self, request):
        payload = request.body
        sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
        endpoint_secret = settings.STRIPE_WEBHOOK_SECRET
        
        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, endpoint_secret
            )
        except ValueError:
            return HttpResponse(status=400)
        except stripe.error.SignatureVerificationError:
            return HttpResponse(status=400)
        
        # Handle the event
        if event['type'] == 'payment_intent.succeeded':
            self.handle_payment_succeeded(event['data']['object'])
        elif event['type'] == 'account.updated':
            self.handle_account_updated(event['data']['object'])
        elif event['type'] == 'transfer.created':
            self.handle_transfer_created(event['data']['object'])
        
        return HttpResponse(status=200)
```

## 📊 Financial Reporting

### **Revenue Analytics**
```python
class FinancialReportView(LoginRequiredMixin, TemplateView):
    template_name = "finance/reports.html"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        
        if user.role == User.RoleChoices.CLIENT:
            context.update(self.get_client_analytics(user.client))
        elif user.role == User.RoleChoices.TALENT:
            context.update(self.get_talent_analytics(user.talent))
        
        return context
    
    def get_client_analytics(self, client):
        # Total spent
        total_spent = Transaction.objects.filter(
            deal__client=client,
            transaction_type=Transaction.TransactionType.PAYMENT,
            status=Transaction.Status.COMPLETED
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        # Monthly spending
        monthly_spending = Transaction.objects.filter(
            deal__client=client,
            transaction_type=Transaction.TransactionType.PAYMENT,
            status=Transaction.Status.COMPLETED,
            created_at__month=timezone.now().month
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        # Active projects
        active_projects = Deal.objects.filter(
            client=client,
            status=Deal.StatusChoices.ACTIVE
        ).count()
        
        return {
            'total_spent': total_spent,
            'monthly_spending': monthly_spending,
            'active_projects': active_projects,
            'avg_project_value': total_spent / max(active_projects, 1)
        }
    
    def get_talent_analytics(self, talent):
        # Total earned
        total_earned = Transaction.objects.filter(
            deal__talent=talent,
            transaction_type=Transaction.TransactionType.PAYOUT,
            status=Transaction.Status.COMPLETED
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        # Monthly earnings
        monthly_earnings = Transaction.objects.filter(
            deal__talent=talent,
            transaction_type=Transaction.TransactionType.PAYOUT,
            status=Transaction.Status.COMPLETED,
            created_at__month=timezone.now().month
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        # Completed projects
        completed_projects = Deal.objects.filter(
            talent=talent,
            status=Deal.StatusChoices.COMPLETED
        ).count()
        
        return {
            'total_earned': total_earned,
            'monthly_earnings': monthly_earnings,
            'completed_projects': completed_projects,
            'avg_project_value': total_earned / max(completed_projects, 1)
        }
```

## 🔒 Security and Compliance

### **PCI Compliance**
```python
class SecurePaymentForm(forms.Form):
    """
    Form that never handles sensitive card data directly
    """
    cardholder_name = forms.CharField(max_length=255)
    billing_address = forms.CharField(widget=forms.Textarea)
    
    # Card details handled by Stripe Elements on frontend
    # Never store or process card numbers server-side
    
    def __init__(self, *args, **kwargs):
        self.stripe_public_key = kwargs.pop('stripe_public_key', None)
        super().__init__(*args, **kwargs)
    
    def get_stripe_context(self):
        return {
            'stripe_public_key': self.stripe_public_key,
            'client_secret': self.get_client_secret()
        }
```

### **Fraud Prevention**
```python
class FraudDetectionService:
    @staticmethod
    def check_transaction_risk(transaction):
        risk_score = 0
        
        # Check transaction amount
        if transaction.amount > 5000:
            risk_score += 20
        
        # Check user history
        user_transactions = Transaction.objects.filter(
            deal__client=transaction.deal.client
        ).count()
        
        if user_transactions == 0:
            risk_score += 30  # First-time user
        
        # Check velocity
        recent_transactions = Transaction.objects.filter(
            deal__client=transaction.deal.client,
            created_at__gte=timezone.now() - timedelta(hours=24)
        ).count()
        
        if recent_transactions > 5:
            risk_score += 40  # High velocity
        
        return risk_score
    
    @staticmethod
    def should_require_verification(transaction):
        return FraudDetectionService.check_transaction_risk(transaction) > 50
```

## 🎯 Key Takeaways

1. **Stripe Integration**: Payment intents and Connect for marketplace
2. **Transaction Tracking**: Comprehensive financial record keeping
3. **Automated Payouts**: Seamless talent payment processing
4. **Financial Reporting**: Analytics for both clients and talents
5. **Security**: PCI compliance and fraud prevention

## 🔗 What's Next?

**Next**: [Lesson 20: Review & Rating System](./20-review-system.md)
