# Lesson 20: Review & Rating System ⭐

## 🎯 Learning Objectives
- Bidirectional review system
- Rating calculation and aggregation
- Review moderation and quality control
- Reputation management
- Review analytics and insights

## ⭐ Review Model Structure

```python
class Review(models.Model):
    class RatingChoices(models.IntegerChoices):
        ONE = 1, "1 Star"
        TWO = 2, "2 Stars"
        THREE = 3, "3 Stars"
        FOUR = 4, "4 Stars"
        FIVE = 5, "5 Stars"
    
    # Relationships
    deal = models.ForeignKey(Deal, on_delete=models.CASCADE)
    reviewer = models.ForeignKey(User, on_delete=models.CASCADE, related_name="given_reviews")
    reviewee = models.ForeignKey(User, on_delete=models.CASCADE, related_name="received_reviews")
    
    # Review content
    rating = models.IntegerField(choices=RatingChoices.choices)
    title = models.CharField(max_length=255)
    content = models.TextField()
    
    # Detailed ratings
    communication_rating = models.IntegerField(choices=RatingChoices.choices)
    quality_rating = models.IntegerField(choices=RatingChoices.choices)
    timeliness_rating = models.IntegerField(choices=RatingChoices.choices)
    professionalism_rating = models.IntegerField(choices=RatingChoices.choices)
    
    # Metadata
    is_public = models.BooleanField(default=True)
    is_verified = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Moderation
    is_flagged = models.BooleanField(default=False)
    moderation_notes = models.TextField(blank=True)
    
    class Meta:
        unique_together = ['deal', 'reviewer']
        indexes = [
            models.Index(fields=['reviewee', 'is_public']),
            models.Index(fields=['rating', 'created_at']),
        ]
```

## 📝 Review Creation Process

### **Review Form**
```python
class ReviewForm(forms.ModelForm):
    class Meta:
        model = Review
        fields = [
            'rating', 'title', 'content',
            'communication_rating', 'quality_rating',
            'timeliness_rating', 'professionalism_rating'
        ]
        widgets = {
            'rating': forms.RadioSelect(),
            'communication_rating': forms.RadioSelect(),
            'quality_rating': forms.RadioSelect(),
            'timeliness_rating': forms.RadioSelect(),
            'professionalism_rating': forms.RadioSelect(),
            'content': forms.Textarea(attrs={'rows': 5}),
        }
    
    def __init__(self, *args, **kwargs):
        self.deal = kwargs.pop('deal', None)
        self.reviewer = kwargs.pop('reviewer', None)
        super().__init__(*args, **kwargs)
    
    def clean(self):
        cleaned_data = super().clean()
        
        # Ensure reviewer can review this deal
        if self.deal and self.reviewer:
            if not (self.reviewer == self.deal.client.user or 
                   self.reviewer == self.deal.talent.user):
                raise forms.ValidationError("You can only review deals you participated in.")
        
        return cleaned_data
    
    def save(self, commit=True):
        review = super().save(commit=False)
        review.deal = self.deal
        review.reviewer = self.reviewer
        
        # Set reviewee (the other party in the deal)
        if self.reviewer == self.deal.client.user:
            review.reviewee = self.deal.talent.user
        else:
            review.reviewee = self.deal.client.user
        
        if commit:
            review.save()
            # Update aggregated ratings
            self.update_user_ratings(review.reviewee)
        
        return review
    
    def update_user_ratings(self, user):
        """Update user's aggregated rating statistics"""
        reviews = Review.objects.filter(reviewee=user, is_public=True)
        
        if reviews.exists():
            avg_rating = reviews.aggregate(avg=Avg('rating'))['avg']
            review_count = reviews.count()
            
            # Update profile ratings
            profile = user.get_profile()
            if profile:
                profile.reviews_rating = round(avg_rating, 2)
                profile.reviews_count = review_count
                profile.save()
```

### **Review Creation View**
```python
class ReviewCreateView(LoginRequiredMixin, CreateView):
    model = Review
    form_class = ReviewForm
    template_name = "reviews/create.html"
    
    def dispatch(self, request, *args, **kwargs):
        self.deal = get_object_or_404(Deal, id=kwargs['deal_id'])
        
        # Check if user can review this deal
        if not (request.user == self.deal.client.user or 
               request.user == self.deal.talent.user):
            raise PermissionDenied
        
        # Check if deal is completed
        if self.deal.status != Deal.StatusChoices.COMPLETED:
            messages.error(request, "You can only review completed deals.")
            return redirect('deals:detail', pk=self.deal.id)
        
        # Check if already reviewed
        if Review.objects.filter(deal=self.deal, reviewer=request.user).exists():
            messages.info(request, "You have already reviewed this deal.")
            return redirect('deals:detail', pk=self.deal.id)
        
        return super().dispatch(request, *args, **kwargs)
    
    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['deal'] = self.deal
        kwargs['reviewer'] = self.request.user
        return kwargs
    
    def form_valid(self, form):
        response = super().form_valid(form)
        
        # Send notification to reviewee
        send_review_notification.delay(self.object.id)
        
        messages.success(
            self.request,
            "Thank you for your review! It helps build trust in our community."
        )
        
        return response
    
    def get_success_url(self):
        return reverse('deals:detail', kwargs={'pk': self.deal.id})
```

## 📊 Rating Analytics

### **User Rating Dashboard**
```python
class RatingAnalyticsView(LoginRequiredMixin, TemplateView):
    template_name = "reviews/analytics.html"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.request.user
        
        # Get received reviews
        received_reviews = Review.objects.filter(
            reviewee=user,
            is_public=True
        ).order_by('-created_at')
        
        # Calculate rating distribution
        rating_distribution = received_reviews.values('rating').annotate(
            count=Count('rating')
        ).order_by('rating')
        
        # Calculate detailed ratings averages
        detailed_ratings = received_reviews.aggregate(
            avg_communication=Avg('communication_rating'),
            avg_quality=Avg('quality_rating'),
            avg_timeliness=Avg('timeliness_rating'),
            avg_professionalism=Avg('professionalism_rating')
        )
        
        # Recent reviews trend
        thirty_days_ago = timezone.now() - timedelta(days=30)
        recent_reviews = received_reviews.filter(created_at__gte=thirty_days_ago)
        
        context.update({
            'received_reviews': received_reviews[:10],
            'total_reviews': received_reviews.count(),
            'average_rating': received_reviews.aggregate(avg=Avg('rating'))['avg'] or 0,
            'rating_distribution': rating_distribution,
            'detailed_ratings': detailed_ratings,
            'recent_reviews_count': recent_reviews.count(),
            'rating_trend': self.calculate_rating_trend(user)
        })
        
        return context
    
    def calculate_rating_trend(self, user):
        """Calculate if rating is trending up or down"""
        reviews = Review.objects.filter(
            reviewee=user,
            is_public=True
        ).order_by('-created_at')[:20]  # Last 20 reviews
        
        if reviews.count() < 10:
            return None
        
        recent_avg = reviews[:10].aggregate(avg=Avg('rating'))['avg']
        older_avg = reviews[10:].aggregate(avg=Avg('rating'))['avg']
        
        if recent_avg > older_avg + 0.2:
            return 'up'
        elif recent_avg < older_avg - 0.2:
            return 'down'
        else:
            return 'stable'
```

## 🛡️ Review Moderation

### **Automated Content Filtering**
```python
class ReviewModerationService:
    INAPPROPRIATE_WORDS = [
        # List of inappropriate words to filter
    ]
    
    @classmethod
    def moderate_review(cls, review):
        """Automatically moderate review content"""
        flags = []
        
        # Check for inappropriate language
        content_lower = review.content.lower()
        for word in cls.INAPPROPRIATE_WORDS:
            if word in content_lower:
                flags.append(f"Contains inappropriate word: {word}")
        
        # Check for extremely negative reviews (potential fake)
        if review.rating == 1 and len(review.content) < 50:
            flags.append("Extremely negative review with minimal content")
        
        # Check for extremely positive reviews (potential fake)
        if review.rating == 5 and "perfect" in content_lower and "amazing" in content_lower:
            flags.append("Potentially fake positive review")
        
        # Check review length
        if len(review.content) < 20:
            flags.append("Review content too short")
        
        if flags:
            review.is_flagged = True
            review.moderation_notes = "; ".join(flags)
            review.is_public = False  # Hide until manual review
            review.save()
            
            # Notify moderators
            notify_moderators_of_flagged_review.delay(review.id)
        
        return flags

# Signal to auto-moderate reviews
@receiver(post_save, sender=Review)
def auto_moderate_review(sender, instance, created, **kwargs):
    if created:
        ReviewModerationService.moderate_review(instance)
```

### **Manual Moderation Interface**
```python
class ReviewModerationView(UserPassesTestMixin, ListView):
    model = Review
    template_name = "admin/review_moderation.html"
    context_object_name = "flagged_reviews"
    
    def test_func(self):
        return self.request.user.is_staff
    
    def get_queryset(self):
        return Review.objects.filter(
            is_flagged=True,
            is_public=False
        ).order_by('-created_at')

class ReviewApprovalView(UserPassesTestMixin, View):
    def test_func(self):
        return self.request.user.is_staff
    
    def post(self, request, review_id):
        review = get_object_or_404(Review, id=review_id)
        action = request.POST.get('action')
        
        if action == 'approve':
            review.is_public = True
            review.is_flagged = False
            review.moderation_notes += f"\nApproved by {request.user.username} on {timezone.now()}"
            
        elif action == 'reject':
            review.is_public = False
            review.moderation_notes += f"\nRejected by {request.user.username} on {timezone.now()}"
            
        elif action == 'edit':
            # Allow moderator to edit content
            review.content = request.POST.get('edited_content', review.content)
            review.is_public = True
            review.is_flagged = False
            review.moderation_notes += f"\nEdited by {request.user.username} on {timezone.now()}"
        
        review.save()
        
        # Update user ratings if approved
        if action in ['approve', 'edit']:
            self.update_user_ratings(review.reviewee)
        
        return JsonResponse({'success': True})
```

## 🏆 Reputation System

### **Reputation Calculation**
```python
class ReputationService:
    @staticmethod
    def calculate_reputation_score(user):
        """Calculate comprehensive reputation score"""
        profile = user.get_profile()
        if not profile:
            return 0
        
        score = 0
        
        # Base score from average rating (0-50 points)
        if profile.reviews_count > 0:
            score += (profile.reviews_rating / 5.0) * 50
        
        # Bonus for number of reviews (0-20 points)
        review_bonus = min(profile.reviews_count * 2, 20)
        score += review_bonus
        
        # Bonus for completed projects (0-20 points)
        completed_projects = Deal.objects.filter(
            talent=profile if hasattr(profile, 'user') and profile.user.role == 'talent' else None,
            client=profile if hasattr(profile, 'user') and profile.user.role == 'client' else None,
            status=Deal.StatusChoices.COMPLETED
        ).count()
        
        project_bonus = min(completed_projects * 3, 20)
        score += project_bonus
        
        # Verification bonus (10 points)
        if hasattr(profile, 'is_verified') and profile.is_verified:
            score += 10
        
        return min(score, 100)  # Cap at 100
    
    @staticmethod
    def get_reputation_level(score):
        """Get reputation level based on score"""
        if score >= 90:
            return "Elite", "🏆"
        elif score >= 80:
            return "Expert", "⭐"
        elif score >= 70:
            return "Professional", "💼"
        elif score >= 60:
            return "Experienced", "📈"
        elif score >= 40:
            return "Developing", "🌱"
        else:
            return "New", "🆕"
```

## 🎯 Key Takeaways

1. **Bidirectional Reviews**: Both clients and talents can review each other
2. **Detailed Ratings**: Multiple aspects rated separately for better insights
3. **Automated Moderation**: Content filtering and quality control
4. **Reputation System**: Comprehensive scoring based on multiple factors
5. **Analytics Dashboard**: Insights for users to improve their performance

## 🔗 What's Next?

Now that you understand all the core features, let's move to advanced topics starting with Stripe integration details.

**Next**: [Lesson 21: Stripe Integration & Webhooks](../05-advanced/21-stripe-integration.md)
