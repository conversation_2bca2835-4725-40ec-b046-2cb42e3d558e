# Generated by Django 3.2.18 on 2024-03-27 14:17

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('clients', '0005_alter_client_options'),
    ]

    operations = [
        migrations.AddField(
            model_name='client',
            name='reviews_count',
            field=models.PositiveIntegerField(default=0, help_text='Denormalized field'),
        ),
        migrations.AddField(
            model_name='client',
            name='reviews_rating',
            field=models.DecimalField(decimal_places=2, default=0, help_text='Denormalized field', max_digits=3),
        ),
        migrations.AddIndex(
            model_name='client',
            index=models.Index(fields=['stripe_customer_id'], name='clients_cli_stripe__fe428a_idx'),
        ),
        migrations.AddIndex(
            model_name='client',
            index=models.Index(fields=['reviews_rating'], name='clients_cli_reviews_fddaff_idx'),
        ),
    ]
