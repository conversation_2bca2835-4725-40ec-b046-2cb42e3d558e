# Lesson 24: Testing Strategies 🧪

## 🎯 Learning Objectives
- Comprehensive testing strategy for Django applications
- Unit, integration, and end-to-end testing
- Testing with external services (Stripe, S3)
- Performance and load testing
- Continuous integration setup

## 🧪 Testing Architecture

### **Test Structure**
```
backend/
├── 📁 apps/
│   ├── 📁 jobs/
│   │   ├── 📁 tests/
│   │   │   ├── 📄 __init__.py
│   │   │   ├── 📄 test_models.py
│   │   │   ├── 📄 test_views.py
│   │   │   ├── 📄 test_forms.py
│   │   │   └── 📄 test_api.py
│   │   └── 📄 factories.py
├── 📁 tests/
│   ├── 📄 test_settings.py
│   ├── 📄 conftest.py
│   └── 📁 integration/
└── 📁 e2e/
    ├── 📄 test_user_journey.py
    └── 📄 test_payment_flow.py
```

### **Test Settings**
```python
# backend/tests/test_settings.py
from backend.settings.base import *

# Test database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }
}

# Disable migrations for faster tests
class DisableMigrations:
    def __contains__(self, item):
        return True
    
    def __getitem__(self, item):
        return None

MIGRATION_MODULES = DisableMigrations()

# Test-specific settings
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.MD5PasswordHasher',  # Faster for tests
]

# Disable caching
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
    }
}

# Use local storage for tests
USE_S3 = False
MEDIA_ROOT = '/tmp/test_media'

# Disable Celery for tests
CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True

# Test Stripe keys
STRIPE_PUBLISHABLE_KEY = 'pk_test_...'
STRIPE_SECRET_KEY = 'sk_test_...'
```

## 🏭 Test Factories

### **Model Factories with Factory Boy**
```python
# backend/apps/registration/factories.py
import factory
from django.contrib.auth import get_user_model
from .models import User

User = get_user_model()

class UserFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = User
    
    email = factory.Sequence(lambda n: f"user{n}@example.com")
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')
    role = User.RoleChoices.TALENT
    is_active = True
    is_email_confirmed = True

class ClientUserFactory(UserFactory):
    role = User.RoleChoices.CLIENT

class TalentUserFactory(UserFactory):
    role = User.RoleChoices.TALENT

# backend/apps/talents/factories.py
class TalentFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Talent
    
    user = factory.SubFactory(TalentUserFactory)
    professional_title = factory.Faker('job')
    about = factory.Faker('text', max_nb_chars=500)
    rate_hourly = factory.Faker('pydecimal', left_digits=3, right_digits=2, positive=True)
    is_verified = True
    is_published = True

# backend/apps/jobs/factories.py
class JobFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Job
    
    client = factory.SubFactory('clients.factories.ClientFactory')
    title = factory.Faker('sentence', nb_words=4)
    description = factory.Faker('text', max_nb_chars=1000)
    category = factory.SubFactory('taxonomy.factories.CategoryFactory')
    budget_min = factory.Faker('pydecimal', left_digits=4, right_digits=2, positive=True)
    budget_max = factory.LazyAttribute(lambda obj: obj.budget_min + 1000)
    status = Job.StatusChoices.PUBLISHED

class DealFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Deal
    
    job = factory.SubFactory(JobFactory)
    talent = factory.SubFactory(TalentFactory)
    client = factory.LazyAttribute(lambda obj: obj.job.client)
    proposal = factory.Faker('text', max_nb_chars=500)
    status = Deal.StatusChoices.PENDING
```

## 🔬 Unit Testing

### **Model Tests**
```python
# backend/apps/jobs/tests/test_models.py
from django.test import TestCase
from django.core.exceptions import ValidationError
from decimal import Decimal
from ..models import Job
from ..factories import JobFactory, DealFactory

class JobModelTest(TestCase):
    def setUp(self):
        self.job = JobFactory()
    
    def test_job_creation(self):
        """Test job is created with correct attributes"""
        self.assertIsNotNone(self.job.id)
        self.assertEqual(self.job.status, Job.StatusChoices.PUBLISHED)
        self.assertGreater(self.job.budget_max, self.job.budget_min)
    
    def test_budget_validation(self):
        """Test budget validation"""
        job = JobFactory.build(budget_min=1000, budget_max=500)
        with self.assertRaises(ValidationError):
            job.full_clean()
    
    def test_get_budget_display(self):
        """Test budget display formatting"""
        job = JobFactory(budget_min=1000, budget_max=1000)
        self.assertEqual(job.get_budget_display(), "$1,000")
        
        job = JobFactory(budget_min=1000, budget_max=2000)
        self.assertEqual(job.get_budget_display(), "$1,000 - $2,000")
    
    def test_is_accepting_applications(self):
        """Test application acceptance logic"""
        published_job = JobFactory(status=Job.StatusChoices.PUBLISHED)
        self.assertTrue(published_job.is_accepting_applications())
        
        draft_job = JobFactory(status=Job.StatusChoices.DRAFT)
        self.assertFalse(draft_job.is_accepting_applications())
    
    def test_publish_method(self):
        """Test job publishing"""
        job = JobFactory(status=Job.StatusChoices.DRAFT, published_at=None)
        job.publish()
        
        self.assertEqual(job.status, Job.StatusChoices.PUBLISHED)
        self.assertIsNotNone(job.published_at)

class DealModelTest(TestCase):
    def test_deal_creation(self):
        """Test deal creation with auto-set client"""
        deal = DealFactory()
        self.assertEqual(deal.client, deal.job.client)
    
    def test_hire_method(self):
        """Test hiring process"""
        deal = DealFactory(status=Deal.StatusChoices.PENDING)
        deal.hire()
        
        self.assertEqual(deal.status, Deal.StatusChoices.ACTIVE)
        self.assertIsNotNone(deal.hired_at)
        
        # Check job status updated
        deal.job.refresh_from_db()
        self.assertEqual(deal.job.status, Job.StatusChoices.IN_PROGRESS)
    
    def test_unique_constraint(self):
        """Test unique constraint on job-talent pair"""
        deal1 = DealFactory()
        
        with self.assertRaises(Exception):
            DealFactory(job=deal1.job, talent=deal1.talent)
```

### **View Tests**
```python
# backend/apps/jobs/tests/test_views.py
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from ..factories import JobFactory, DealFactory
from registration.factories import ClientUserFactory, TalentUserFactory

User = get_user_model()

class JobViewTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.talent_user = TalentUserFactory()
        self.client_user = ClientUserFactory()
    
    def test_job_list_view(self):
        """Test job listing page"""
        JobFactory.create_batch(5)
        
        response = self.client.get(reverse('jobs:list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'jobs')
    
    def test_job_detail_view(self):
        """Test job detail page"""
        job = JobFactory()
        
        response = self.client.get(reverse('jobs:detail', kwargs={'pk': job.pk}))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, job.title)
    
    def test_job_create_requires_client(self):
        """Test job creation requires client role"""
        # Anonymous user
        response = self.client.get(reverse('jobs:create'))
        self.assertEqual(response.status_code, 302)  # Redirect to login
        
        # Talent user
        self.client.force_login(self.talent_user)
        response = self.client.get(reverse('jobs:create'))
        self.assertEqual(response.status_code, 403)  # Forbidden
        
        # Client user
        self.client.force_login(self.client_user)
        response = self.client.get(reverse('jobs:create'))
        self.assertEqual(response.status_code, 200)
    
    def test_job_application_creation(self):
        """Test job application process"""
        job = JobFactory()
        self.client.force_login(self.talent_user)
        
        response = self.client.post(
            reverse('jobs:apply', kwargs={'pk': job.pk}),
            {'proposal': 'I would like to work on this project.'}
        )
        
        self.assertEqual(response.status_code, 302)  # Redirect after success
        self.assertTrue(
            Deal.objects.filter(job=job, talent=self.talent_user.talent).exists()
        )
    
    def test_job_search_filtering(self):
        """Test job search with filters"""
        job1 = JobFactory(title="Python Developer")
        job2 = JobFactory(title="JavaScript Developer")
        
        # Search by title
        response = self.client.get(reverse('jobs:search'), {'q': 'Python'})
        self.assertContains(response, job1.title)
        self.assertNotContains(response, job2.title)
        
        # Search by category
        response = self.client.get(
            reverse('jobs:search'), 
            {'category': job1.category.slug}
        )
        self.assertContains(response, job1.title)
```

## 🔗 Integration Testing

### **API Integration Tests**
```python
# backend/tests/integration/test_api_integration.py
from django.test import TestCase, TransactionTestCase
from django.test.utils import override_settings
from rest_framework.test import APIClient
from rest_framework import status
import json

class JobAPIIntegrationTest(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.talent_user = TalentUserFactory()
        self.client_user = ClientUserFactory()
    
    def test_complete_job_workflow(self):
        """Test complete job posting and application workflow"""
        # 1. Client creates job
        self.client.force_authenticate(user=self.client_user)
        job_data = {
            'title': 'Test Job',
            'description': 'Test description',
            'budget_min': 1000,
            'budget_max': 2000,
            'category': CategoryFactory().id
        }
        
        response = self.client.post('/api/jobs/', job_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        job_id = response.data['id']
        
        # 2. Talent applies for job
        self.client.force_authenticate(user=self.talent_user)
        application_data = {
            'proposal': 'I would like to work on this project.'
        }
        
        response = self.client.post(f'/api/jobs/{job_id}/apply/', application_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        deal_id = response.data['id']
        
        # 3. Client hires talent
        self.client.force_authenticate(user=self.client_user)
        hire_data = {
            'agreed_rate': 1500,
            'start_date': '2024-01-01',
            'end_date': '2024-02-01'
        }
        
        response = self.client.post(f'/api/deals/{deal_id}/hire/', hire_data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # 4. Verify deal status
        response = self.client.get(f'/api/deals/{deal_id}/')
        self.assertEqual(response.data['status'], 'active')
```

### **Payment Integration Tests**
```python
# backend/tests/integration/test_payment_integration.py
import stripe
from unittest.mock import patch, MagicMock
from django.test import TestCase, override_settings

@override_settings(STRIPE_SECRET_KEY='sk_test_fake')
class PaymentIntegrationTest(TestCase):
    def setUp(self):
        self.deal = DealFactory(status=Deal.StatusChoices.ACTIVE)
        self.milestone = MilestoneFactory(deal=self.deal)
    
    @patch('stripe.PaymentIntent.create')
    def test_payment_intent_creation(self, mock_create):
        """Test Stripe payment intent creation"""
        mock_intent = MagicMock()
        mock_intent.id = 'pi_test_123'
        mock_intent.client_secret = 'pi_test_123_secret'
        mock_create.return_value = mock_intent
        
        from finance.services import PaymentService
        intent, transaction = PaymentService.create_payment_intent(
            self.milestone, 
            self.deal.client
        )
        
        self.assertEqual(intent.id, 'pi_test_123')
        self.assertEqual(transaction.stripe_payment_intent_id, 'pi_test_123')
        mock_create.assert_called_once()
    
    @patch('stripe.PaymentIntent.capture')
    @patch('stripe.Transfer.create')
    def test_escrow_release(self, mock_transfer, mock_capture):
        """Test escrow payment release"""
        # Setup mocks
        mock_capture.return_value = MagicMock(status='succeeded')
        mock_transfer.return_value = MagicMock(id='tr_test_123')
        
        transaction = TransactionFactory(
            deal=self.deal,
            milestone=self.milestone,
            stripe_payment_intent_id='pi_test_123'
        )
        
        from finance.services import EscrowService
        transfer = EscrowService.release_escrow_payment(transaction)
        
        transaction.refresh_from_db()
        self.assertEqual(transaction.status, Transaction.Status.COMPLETED)
        mock_capture.assert_called_once_with('pi_test_123')
        mock_transfer.assert_called_once()
```

## 🎭 End-to-End Testing

### **Selenium Tests**
```python
# backend/e2e/test_user_journey.py
from django.contrib.staticfiles.testing import StaticLiveServerTestCase
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

class UserJourneyTest(StaticLiveServerTestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        cls.selenium = webdriver.Chrome(options=chrome_options)
        cls.selenium.implicitly_wait(10)
    
    @classmethod
    def tearDownClass(cls):
        cls.selenium.quit()
        super().tearDownClass()
    
    def test_talent_registration_and_profile_setup(self):
        """Test complete talent registration flow"""
        # 1. Go to registration page
        self.selenium.get(f'{self.live_server_url}/signup/?role=talent')
        
        # 2. Fill registration form
        self.selenium.find_element(By.NAME, 'email').send_keys('<EMAIL>')
        self.selenium.find_element(By.NAME, 'first_name').send_keys('John')
        self.selenium.find_element(By.NAME, 'last_name').send_keys('Doe')
        self.selenium.find_element(By.NAME, 'password1').send_keys('testpass123')
        self.selenium.find_element(By.NAME, 'password2').send_keys('testpass123')
        self.selenium.find_element(By.NAME, 'terms_accepted').click()
        
        # 3. Submit form
        self.selenium.find_element(By.CSS_SELECTOR, 'button[type="submit"]').click()
        
        # 4. Check redirect to email confirmation page
        WebDriverWait(self.selenium, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, 'email-confirmation'))
        )
        
        # 5. Simulate email confirmation (in real test, would click email link)
        user = User.objects.get(email='<EMAIL>')
        user.is_active = True
        user.is_email_confirmed = True
        user.save()
        
        # 6. Login
        self.selenium.get(f'{self.live_server_url}/login/')
        self.selenium.find_element(By.NAME, 'username').send_keys('<EMAIL>')
        self.selenium.find_element(By.NAME, 'password').send_keys('testpass123')
        self.selenium.find_element(By.CSS_SELECTOR, 'button[type="submit"]').click()
        
        # 7. Check redirect to profile setup
        WebDriverWait(self.selenium, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, 'profile-setup'))
        )
    
    def test_job_posting_and_application_flow(self):
        """Test job posting by client and application by talent"""
        # Setup test data
        client = ClientUserFactory(email='<EMAIL>')
        talent = TalentUserFactory(email='<EMAIL>')
        
        # 1. Client logs in and posts job
        self.selenium.get(f'{self.live_server_url}/login/')
        self.selenium.find_element(By.NAME, 'username').send_keys('<EMAIL>')
        self.selenium.find_element(By.NAME, 'password').send_keys('testpass123')
        self.selenium.find_element(By.CSS_SELECTOR, 'button[type="submit"]').click()
        
        # 2. Navigate to job creation
        self.selenium.get(f'{self.live_server_url}/jobs/create/')
        
        # 3. Fill job form
        self.selenium.find_element(By.NAME, 'title').send_keys('Test Job')
        self.selenium.find_element(By.NAME, 'description').send_keys('Test description')
        self.selenium.find_element(By.NAME, 'budget_min').send_keys('1000')
        self.selenium.find_element(By.NAME, 'budget_max').send_keys('2000')
        
        # 4. Submit job
        self.selenium.find_element(By.CSS_SELECTOR, 'button[type="submit"]').click()
        
        # 5. Logout and login as talent
        self.selenium.get(f'{self.live_server_url}/logout/')
        self.selenium.get(f'{self.live_server_url}/login/')
        self.selenium.find_element(By.NAME, 'username').send_keys('<EMAIL>')
        self.selenium.find_element(By.NAME, 'password').send_keys('testpass123')
        self.selenium.find_element(By.CSS_SELECTOR, 'button[type="submit"]').click()
        
        # 6. Find and apply for job
        self.selenium.get(f'{self.live_server_url}/jobs/')
        job_link = self.selenium.find_element(By.PARTIAL_LINK_TEXT, 'Test Job')
        job_link.click()
        
        # 7. Submit application
        self.selenium.find_element(By.NAME, 'proposal').send_keys('I would like to work on this.')
        self.selenium.find_element(By.CSS_SELECTOR, 'button.apply-btn').click()
        
        # 8. Verify application submitted
        WebDriverWait(self.selenium, 10).until(
            EC.presence_of_element_located((By.CLASS_NAME, 'application-success'))
        )
```

## 📊 Performance Testing

### **Load Testing with Locust**
```python
# backend/tests/performance/locustfile.py
from locust import HttpUser, task, between
import random

class CrelancerUser(HttpUser):
    wait_time = between(1, 3)
    
    def on_start(self):
        """Login user"""
        self.client.post("/login/", {
            "username": "<EMAIL>",
            "password": "testpass123"
        })
    
    @task(3)
    def browse_jobs(self):
        """Browse job listings"""
        self.client.get("/jobs/")
        
        # Search jobs
        self.client.get("/jobs/search/", params={
            "q": random.choice(["python", "javascript", "design"])
        })
    
    @task(2)
    def view_job_detail(self):
        """View job details"""
        job_id = random.randint(1, 100)
        self.client.get(f"/jobs/{job_id}/")
    
    @task(1)
    def browse_talents(self):
        """Browse talent profiles"""
        self.client.get("/talents/")
        
        talent_id = random.randint(1, 50)
        self.client.get(f"/talents/{talent_id}/")
    
    @task(1)
    def check_messages(self):
        """Check chat messages"""
        self.client.get("/chat/")

class TalentUser(CrelancerUser):
    @task(2)
    def apply_for_jobs(self):
        """Apply for jobs"""
        job_id = random.randint(1, 100)
        self.client.post(f"/jobs/{job_id}/apply/", {
            "proposal": "I would like to work on this project."
        })

class ClientUser(CrelancerUser):
    @task(2)
    def post_job(self):
        """Post new job"""
        self.client.post("/jobs/create/", {
            "title": f"Test Job {random.randint(1, 1000)}",
            "description": "Test job description",
            "budget_min": 1000,
            "budget_max": 2000,
            "category": random.randint(1, 10)
        })
```

## 🎯 Key Takeaways

1. **Comprehensive Coverage**: Unit, integration, and E2E tests
2. **Test Factories**: Consistent test data generation
3. **Mocking External Services**: Isolate tests from third-party APIs
4. **Performance Testing**: Load testing with realistic scenarios
5. **CI/CD Integration**: Automated testing in deployment pipeline

## 🔗 What's Next?

**Next**: [Lesson 25: Performance Optimization](./25-performance.md)
