from django.db import models


class Client(models.Model):
    """
    Client profile. Contains information about the company.
    """

    user = models.OneToOneField("registration.User", on_delete=models.CASCADE)
    about = models.TextField(null=True, blank=True)
    website = models.URLField(null=True, blank=True)
    stripe_customer_id = models.CharField(max_length=255, null=True)
    stripe_card_data = models.JSONField(null=True)

    reviews_rating = models.DecimalField(
        max_digits=3, decimal_places=2, default=0, help_text="Denormalized field"
    )
    reviews_count = models.PositiveIntegerField(
        default=0, help_text="Denormalized field"
    )

    class Meta:
        verbose_name = "Client Profile"
        verbose_name_plural = " Client Profiles"
        indexes = [
            models.Index(fields=["stripe_customer_id"]),
            models.Index(fields=["reviews_rating"]),
        ]
        default_permissions = ("view", "change")

    def __str__(self):
        if self.user.company:
            return f"{self.user.get_full_name()}, {self.user.company}"
        return self.user.get_full_name()

    @property
    def has_payment_method(self):
        return bool(self.stripe_card_data not in [None, {}])
