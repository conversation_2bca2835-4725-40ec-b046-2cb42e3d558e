from django.urls import reverse_lazy
from django.views.generic import TemplateView
from django.views.generic.edit import CreateView, UpdateView
from django.utils.decorators import method_decorator
from django.contrib.auth.decorators import login_required
from .models import Client
from .forms import ClientBasicsForm
from .decorators import logined_client_required


@method_decorator(login_required(), name="dispatch")
class ClientCreateView(CreateView):
    """
    The page for creating a Client profile in case if
    the Talent wants to become a Client
    """

    model = Client
    fields = ["about", "website"]
    template_name = "clients/create.html"
    success_url = reverse_lazy("clients:profile")

    def form_valid(self, form):
        form.instance.user = self.request.user
        return super().form_valid(form)


@method_decorator(logined_client_required("clients:not_a_client"), name="dispatch")
class ClientProfileView(TemplateView):
    """
    The main profile page for Client users
    """

    template_name = "clients/profile.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context["client"] = Client.objects.get(user=self.request.user)
        return context


@method_decorator(logined_client_required(), name="dispatch")
class BasicShowView(TemplateView):
    template_name = "clients/frames/basic_show.html"


@method_decorator(logined_client_required(), name="dispatch")
class BasicChangeView(UpdateView):
    model = Client
    form_class = ClientBasicsForm
    template_name = "clients/frames/basic_change.html"
    success_url = reverse_lazy("clients:basic_show")

    def get_object(self, queryset=None):
        return self.request.user.client
