from django.core.management.base import BaseCommand
from django.utils import timezone
from chat.models import MessageNotification
from chat.signals import chat_message_notification_time


class Command(BaseCommand):
    help = "We send a signal to notify the user about unread messages with age more than 1 hour."
    limit = 10

    def handle(self, *args, **options):
        # items = MessageNotification.objects.all()[: self.limit]
        items = MessageNotification.objects.filter(
            last_message_at__lte=timezone.now() - timezone.timedelta(hours=1)
        )[: self.limit]
        if items:
            for item in items:

                # send a signal to catch by notification service
                chat_message_notification_time.send(
                    sender=self.__class__, notification=item
                )

                self.stdout.write(
                    self.style.SUCCESS('Successfully notified "%s"' % item.user)
                )
                item.delete()
        else:
            self.stdout.write(self.style.SUCCESS("No notifications to send."))
