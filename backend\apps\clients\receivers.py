from django.dispatch import receiver
from django.db.models.signals import post_save, post_delete
from registration.signals import signup_confirmed
from clients.models import Client
from reviews.models import Review
from registration.models import User


@receiver(signup_confirmed)
def create_client_profile(sender, user, **kwargs):
    if user.role != user.RoleChoices.CLIENT:
        return

    Client.objects.create(user=user)


@receiver(post_save, sender=Review)
def update_average_rating(sender, instance: Review, created, **kwargs):
    if not created or instance.author_type != Review.AuthorType.TALENT:
        return

    client = instance.deal.client

    data = Review.objects.client_rating_data(client.pk)

    client.reviews_rating = data["rating"]
    client.reviews_count = data["count"]
    client.save()


@receiver(post_delete, sender=User)
def deactivate_and_obfuscate_client(sender, instance: User, **kwargs):
    if not instance.is_client:
        return

    obj = instance.client

    obj.about = "Deleted"
    obj.website = None

    # obj.stripe_customer_id = None
    # obj.stripe_card_data = None

    obj.save()
