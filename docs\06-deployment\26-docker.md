# Lesson 26: Docker & Containerization 🐳

## 🎯 Learning Objectives
- Docker containerization for Django applications
- Multi-stage builds and optimization
- Docker Compose for development
- Production deployment strategies
- Container orchestration basics

## 🐳 Docker Configuration

### **Dockerfile for Django**
```dockerfile
# Multi-stage build for production optimization
FROM node:18-alpine AS frontend-builder

WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm ci --only=production

COPY frontend/ ./
RUN npm run build

# Python base image
FROM python:3.11-slim AS backend-base

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app user
RUN groupadd -r app && useradd -r -g app app

# Set work directory
WORKDIR /app

# Install Python dependencies
COPY requirements/production.txt ./requirements.txt
RUN pip install -r requirements.txt

# Development stage
FROM backend-base AS development

# Install development dependencies
COPY requirements/development.txt ./requirements-dev.txt
RUN pip install -r requirements-dev.txt

# Copy application code
COPY backend/ ./backend/
COPY --from=frontend-builder /app/frontend/build ./frontend/build/

# Change ownership
RUN chown -R app:app /app
USER app

EXPOSE 8000
CMD ["python", "backend/manage.py", "runserver", "0.0.0.0:8000"]

# Production stage
FROM backend-base AS production

# Copy application code
COPY backend/ ./backend/
COPY --from=frontend-builder /app/frontend/build ./frontend/build/

# Collect static files
RUN python backend/manage.py collectstatic --noinput

# Change ownership
RUN chown -R app:app /app
USER app

EXPOSE 8000
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "3", "backend.wsgi:application"]
```

### **Docker Compose for Development**
```yaml
# docker-compose.yml
version: '3.8'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: crelancer_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  web:
    build:
      context: .
      target: development
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - /app/backend/staticfiles
      - /app/frontend/node_modules
    environment:
      - DEBUG=1
      - DATABASE_URL=**************************************/crelancer_dev
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    command: >
      sh -c "python backend/manage.py migrate &&
             python backend/manage.py runserver 0.0.0.0:8000"

  celery:
    build:
      context: .
      target: development
    volumes:
      - .:/app
    environment:
      - DEBUG=1
      - DATABASE_URL=**************************************/crelancer_dev
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    command: celery -A backend worker -l info

  celery-beat:
    build:
      context: .
      target: development
    volumes:
      - .:/app
    environment:
      - DEBUG=1
      - DATABASE_URL=**************************************/crelancer_dev
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    command: celery -A backend beat -l info

  frontend:
    image: node:18-alpine
    working_dir: /app
    volumes:
      - ./frontend:/app
    ports:
      - "3000:3000"
    command: >
      sh -c "npm install &&
             npm run start"
    environment:
      - NODE_ENV=development

volumes:
  postgres_data:
  redis_data:
```

### **Production Docker Compose**
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    depends_on:
      - web
    restart: unless-stopped

  web:
    build:
      context: .
      target: production
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
    environment:
      - DEBUG=0
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - SECRET_KEY=${SECRET_KEY}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
    depends_on:
      - db
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  celery:
    build:
      context: .
      target: production
    environment:
      - DEBUG=0
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CELERY_BROKER_URL=${REDIS_URL}
    depends_on:
      - db
      - redis
    restart: unless-stopped
    command: celery -A backend worker -l info --concurrency=4

  celery-beat:
    build:
      context: .
      target: production
    environment:
      - DEBUG=0
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - CELERY_BROKER_URL=${REDIS_URL}
    depends_on:
      - db
      - redis
    restart: unless-stopped
    command: celery -A backend beat -l info

volumes:
  postgres_data:
  redis_data:
  static_volume:
  media_volume:
```

## 🔧 Nginx Configuration

### **Nginx Configuration**
```nginx
# nginx/nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream web {
        server web:8000;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    server {
        listen 80;
        server_name crelancer.com www.crelancer.com;
        
        # Redirect HTTP to HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name crelancer.com www.crelancer.com;

        # SSL configuration
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
        ssl_prefer_server_ciphers off;

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

        # Static files
        location /static/ {
            alias /app/staticfiles/;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        location /media/ {
            alias /app/media/;
            expires 1y;
            add_header Cache-Control "public";
        }

        # API rate limiting
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://web;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Host $host;
            proxy_redirect off;
        }

        # Login rate limiting
        location /login/ {
            limit_req zone=login burst=5 nodelay;
            proxy_pass http://web;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Host $host;
            proxy_redirect off;
        }

        # Main application
        location / {
            proxy_pass http://web;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Host $host;
            proxy_redirect off;
            
            # WebSocket support
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }

        # Health check
        location /health/ {
            access_log off;
            proxy_pass http://web;
        }
    }
}
```

## 🚀 Deployment Scripts

### **Deployment Script**
```bash
#!/bin/bash
# scripts/deploy.sh

set -e

echo "🚀 Starting Crelancer deployment..."

# Configuration
ENVIRONMENT=${1:-production}
COMPOSE_FILE="docker-compose.prod.yml"

# Load environment variables
if [ -f ".env.${ENVIRONMENT}" ]; then
    export $(cat .env.${ENVIRONMENT} | xargs)
fi

# Pre-deployment checks
echo "🔍 Running pre-deployment checks..."

# Check if required environment variables are set
required_vars=("DATABASE_URL" "SECRET_KEY" "STRIPE_SECRET_KEY")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo "❌ Error: $var is not set"
        exit 1
    fi
done

# Build and deploy
echo "🏗️ Building Docker images..."
docker-compose -f $COMPOSE_FILE build --no-cache

echo "📦 Pulling latest images..."
docker-compose -f $COMPOSE_FILE pull

# Database backup
echo "💾 Creating database backup..."
./scripts/backup-db.sh

# Deploy with zero downtime
echo "🔄 Deploying with zero downtime..."

# Start new containers
docker-compose -f $COMPOSE_FILE up -d --no-deps web

# Wait for health check
echo "⏳ Waiting for health check..."
timeout 60 bash -c 'until curl -f http://localhost:8000/health/; do sleep 2; done'

# Run migrations
echo "🗄️ Running database migrations..."
docker-compose -f $COMPOSE_FILE exec web python backend/manage.py migrate

# Collect static files
echo "📁 Collecting static files..."
docker-compose -f $COMPOSE_FILE exec web python backend/manage.py collectstatic --noinput

# Restart all services
echo "🔄 Restarting all services..."
docker-compose -f $COMPOSE_FILE up -d

# Clean up old images
echo "🧹 Cleaning up old images..."
docker image prune -f

echo "✅ Deployment completed successfully!"

# Post-deployment verification
echo "🔍 Running post-deployment checks..."
./scripts/health-check.sh

echo "🎉 Crelancer is now live!"
```

### **Health Check Script**
```bash
#!/bin/bash
# scripts/health-check.sh

set -e

BASE_URL=${BASE_URL:-"http://localhost:8000"}
TIMEOUT=30

echo "🏥 Running health checks for Crelancer..."

# Function to check endpoint
check_endpoint() {
    local endpoint=$1
    local expected_status=${2:-200}
    
    echo "Checking $endpoint..."
    
    response=$(curl -s -o /dev/null -w "%{http_code}" --max-time $TIMEOUT "$BASE_URL$endpoint")
    
    if [ "$response" -eq "$expected_status" ]; then
        echo "✅ $endpoint - OK ($response)"
        return 0
    else
        echo "❌ $endpoint - FAILED ($response)"
        return 1
    fi
}

# Health checks
failed=0

check_endpoint "/health/" || failed=1
check_endpoint "/" || failed=1
check_endpoint "/api/health/" || failed=1

# Database connectivity
echo "Checking database connectivity..."
if docker-compose exec -T web python backend/manage.py check --database default; then
    echo "✅ Database - OK"
else
    echo "❌ Database - FAILED"
    failed=1
fi

# Redis connectivity
echo "Checking Redis connectivity..."
if docker-compose exec -T redis redis-cli ping | grep -q PONG; then
    echo "✅ Redis - OK"
else
    echo "❌ Redis - FAILED"
    failed=1
fi

# Celery workers
echo "Checking Celery workers..."
if docker-compose exec -T celery celery -A backend inspect ping | grep -q OK; then
    echo "✅ Celery - OK"
else
    echo "❌ Celery - FAILED"
    failed=1
fi

if [ $failed -eq 0 ]; then
    echo "🎉 All health checks passed!"
    exit 0
else
    echo "💥 Some health checks failed!"
    exit 1
fi
```

## 📊 Monitoring and Logging

### **Docker Logging Configuration**
```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  web:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=web,environment=production"

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin

volumes:
  prometheus_data:
  grafana_data:
```

### **Application Metrics**
```python
# backend/core/metrics.py
from prometheus_client import Counter, Histogram, Gauge
import time

# Metrics
REQUEST_COUNT = Counter(
    'django_requests_total',
    'Total Django requests',
    ['method', 'endpoint', 'status']
)

REQUEST_DURATION = Histogram(
    'django_request_duration_seconds',
    'Django request duration',
    ['method', 'endpoint']
)

ACTIVE_USERS = Gauge(
    'django_active_users',
    'Number of active users'
)

class MetricsMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        start_time = time.time()
        
        response = self.get_response(request)
        
        # Record metrics
        REQUEST_COUNT.labels(
            method=request.method,
            endpoint=request.resolver_match.url_name if request.resolver_match else 'unknown',
            status=response.status_code
        ).inc()
        
        REQUEST_DURATION.labels(
            method=request.method,
            endpoint=request.resolver_match.url_name if request.resolver_match else 'unknown'
        ).observe(time.time() - start_time)
        
        return response
```

## 🎯 Key Takeaways

1. **Multi-stage Builds**: Optimize Docker images for production
2. **Container Orchestration**: Use Docker Compose for service management
3. **Zero Downtime Deployment**: Health checks and rolling updates
4. **Security**: Proper SSL configuration and security headers
5. **Monitoring**: Comprehensive logging and metrics collection

## 🔗 What's Next?

**Next**: [Lesson 27: Production Deployment](./27-production-deployment.md)
