# Lesson 28: Monitoring & Maintenance 📊

## 🎯 Learning Objectives
- Application performance monitoring
- Error tracking and alerting
- Log management and analysis
- Database maintenance and optimization
- Backup and disaster recovery

## 📊 Application Monitoring

### **Sentry Integration for Error Tracking**
```python
# backend/settings/production.py
import sentry_sdk
from sentry_sdk.integrations.django import DjangoIntegration
from sentry_sdk.integrations.celery import CeleryIntegration
from sentry_sdk.integrations.redis import RedisIntegration

sentry_sdk.init(
    dsn=os.environ.get('SENTRY_DSN'),
    integrations=[
        DjangoIntegration(
            transaction_style='url',
            middleware_spans=True,
            signals_spans=True,
            cache_spans=True,
        ),
        CeleryIntegration(
            monitor_beat_tasks=True,
            propagate_traces=True,
        ),
        RedisIntegration(),
    ],
    traces_sample_rate=0.1,
    send_default_pii=False,
    environment=os.environ.get('ENVIRONMENT', 'production'),
    release=os.environ.get('GIT_COMMIT_SHA'),
)

# Custom error handling
class SentryMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Add user context to Sentry
        if request.user.is_authenticated:
            sentry_sdk.set_user({
                "id": request.user.id,
                "email": request.user.email,
                "role": request.user.role,
            })
        
        # Add request context
        sentry_sdk.set_context("request", {
            "url": request.build_absolute_uri(),
            "method": request.method,
            "headers": dict(request.headers),
        })
        
        response = self.get_response(request)
        return response
```

### **Custom Metrics and Dashboards**
```python
# backend/core/metrics.py
from django.core.cache import cache
from django.db.models import Count, Avg, Sum
from datetime import datetime, timedelta
import json

class BusinessMetrics:
    @staticmethod
    def get_daily_metrics():
        """Get key business metrics for the day"""
        today = datetime.now().date()
        
        metrics = {
            'new_users': User.objects.filter(date_joined__date=today).count(),
            'new_jobs': Job.objects.filter(created_at__date=today).count(),
            'new_deals': Deal.objects.filter(created_at__date=today).count(),
            'completed_deals': Deal.objects.filter(
                status=Deal.StatusChoices.COMPLETED,
                updated_at__date=today
            ).count(),
            'revenue': Transaction.objects.filter(
                transaction_type=Transaction.TransactionType.FEE,
                created_at__date=today,
                status=Transaction.Status.COMPLETED
            ).aggregate(total=Sum('amount'))['total'] or 0,
        }
        
        return metrics
    
    @staticmethod
    def get_weekly_trends():
        """Get weekly trend data"""
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=7)
        
        # User registrations by day
        user_trends = User.objects.filter(
            date_joined__date__range=[start_date, end_date]
        ).extra(
            select={'day': 'date(date_joined)'}
        ).values('day').annotate(count=Count('id')).order_by('day')
        
        # Job postings by day
        job_trends = Job.objects.filter(
            created_at__date__range=[start_date, end_date]
        ).extra(
            select={'day': 'date(created_at)'}
        ).values('day').annotate(count=Count('id')).order_by('day')
        
        return {
            'user_registrations': list(user_trends),
            'job_postings': list(job_trends),
        }
    
    @staticmethod
    def get_platform_health():
        """Get platform health indicators"""
        # Average response time (from cache)
        avg_response_time = cache.get('avg_response_time', 0)
        
        # Error rate (last hour)
        error_count = cache.get('error_count_1h', 0)
        request_count = cache.get('request_count_1h', 1)
        error_rate = (error_count / request_count) * 100
        
        # Active users (last 24 hours)
        active_users = User.objects.filter(
            last_login__gte=datetime.now() - timedelta(hours=24)
        ).count()
        
        # Database connection pool
        from django.db import connection
        db_queries = len(connection.queries)
        
        return {
            'avg_response_time': avg_response_time,
            'error_rate': error_rate,
            'active_users': active_users,
            'db_queries': db_queries,
        }

# Metrics collection middleware
class MetricsCollectionMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        start_time = time.time()
        
        response = self.get_response(request)
        
        # Collect response time
        response_time = time.time() - start_time
        self.update_response_time_metric(response_time)
        
        # Count requests and errors
        self.increment_request_counter()
        if response.status_code >= 400:
            self.increment_error_counter()
        
        return response
    
    def update_response_time_metric(self, response_time):
        """Update average response time"""
        current_avg = cache.get('avg_response_time', 0)
        current_count = cache.get('response_count', 0)
        
        new_count = current_count + 1
        new_avg = ((current_avg * current_count) + response_time) / new_count
        
        cache.set('avg_response_time', new_avg, 3600)
        cache.set('response_count', new_count, 3600)
    
    def increment_request_counter(self):
        """Increment request counter"""
        cache_key = 'request_count_1h'
        current_count = cache.get(cache_key, 0)
        cache.set(cache_key, current_count + 1, 3600)
    
    def increment_error_counter(self):
        """Increment error counter"""
        cache_key = 'error_count_1h'
        current_count = cache.get(cache_key, 0)
        cache.set(cache_key, current_count + 1, 3600)
```

### **Health Check Endpoints**
```python
# backend/core/health.py
from django.http import JsonResponse
from django.views import View
from django.db import connection
from django.core.cache import cache
import redis
import time

class HealthCheckView(View):
    def get(self, request):
        """Comprehensive health check"""
        health_status = {
            'status': 'healthy',
            'timestamp': time.time(),
            'checks': {}
        }
        
        # Database check
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            health_status['checks']['database'] = {'status': 'healthy'}
        except Exception as e:
            health_status['checks']['database'] = {
                'status': 'unhealthy',
                'error': str(e)
            }
            health_status['status'] = 'unhealthy'
        
        # Redis check
        try:
            cache.set('health_check', 'ok', 10)
            cache.get('health_check')
            health_status['checks']['redis'] = {'status': 'healthy'}
        except Exception as e:
            health_status['checks']['redis'] = {
                'status': 'unhealthy',
                'error': str(e)
            }
            health_status['status'] = 'unhealthy'
        
        # Celery check
        try:
            from celery import current_app
            inspect = current_app.control.inspect()
            stats = inspect.stats()
            if stats:
                health_status['checks']['celery'] = {'status': 'healthy'}
            else:
                health_status['checks']['celery'] = {
                    'status': 'unhealthy',
                    'error': 'No workers available'
                }
                health_status['status'] = 'degraded'
        except Exception as e:
            health_status['checks']['celery'] = {
                'status': 'unhealthy',
                'error': str(e)
            }
            health_status['status'] = 'unhealthy'
        
        # Return appropriate status code
        status_code = 200 if health_status['status'] == 'healthy' else 503
        return JsonResponse(health_status, status=status_code)

class ReadinessCheckView(View):
    def get(self, request):
        """Readiness check for load balancer"""
        try:
            # Quick database check
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
            
            return JsonResponse({'status': 'ready'})
        except Exception:
            return JsonResponse({'status': 'not ready'}, status=503)
```

## 📝 Log Management

### **Structured Logging Configuration**
```python
# backend/settings/logging.py
import os

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'json': {
            '()': 'pythonjsonlogger.jsonlogger.JsonFormatter',
            'format': '%(asctime)s %(name)s %(levelname)s %(message)s'
        },
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'json' if os.environ.get('ENVIRONMENT') == 'production' else 'verbose',
        },
        'file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/crelancer/app.log',
            'maxBytes': 1024*1024*10,  # 10MB
            'backupCount': 5,
            'formatter': 'json',
        },
        'sentry': {
            'class': 'sentry_sdk.integrations.logging.SentryHandler',
            'level': 'ERROR',
        },
    },
    'root': {
        'level': 'INFO',
        'handlers': ['console', 'file', 'sentry'],
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
        'crelancer': {
            'handlers': ['console', 'file', 'sentry'],
            'level': 'INFO',
            'propagate': False,
        },
        'celery': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# Custom logging middleware
class RequestLoggingMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        self.logger = logging.getLogger('crelancer.requests')

    def __call__(self, request):
        start_time = time.time()
        
        # Log request
        self.logger.info('Request started', extra={
            'method': request.method,
            'path': request.path,
            'user_id': request.user.id if request.user.is_authenticated else None,
            'ip_address': self.get_client_ip(request),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
        })
        
        response = self.get_response(request)
        
        # Log response
        duration = time.time() - start_time
        self.logger.info('Request completed', extra={
            'method': request.method,
            'path': request.path,
            'status_code': response.status_code,
            'duration': duration,
            'user_id': request.user.id if request.user.is_authenticated else None,
        })
        
        return response
    
    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
```

### **Log Analysis and Alerting**
```python
# scripts/log_analyzer.py
import json
import re
from collections import defaultdict, Counter
from datetime import datetime, timedelta

class LogAnalyzer:
    def __init__(self, log_file_path):
        self.log_file_path = log_file_path
    
    def analyze_errors(self, hours=24):
        """Analyze error patterns in the last N hours"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        error_patterns = defaultdict(int)
        error_details = []
        
        with open(self.log_file_path, 'r') as f:
            for line in f:
                try:
                    log_entry = json.loads(line)
                    log_time = datetime.fromisoformat(log_entry['asctime'])
                    
                    if log_time > cutoff_time and log_entry['levelname'] == 'ERROR':
                        error_patterns[log_entry['message']] += 1
                        error_details.append(log_entry)
                        
                except (json.JSONDecodeError, KeyError, ValueError):
                    continue
        
        return {
            'total_errors': len(error_details),
            'error_patterns': dict(error_patterns),
            'top_errors': Counter(error_patterns).most_common(10),
            'recent_errors': error_details[-10:]  # Last 10 errors
        }
    
    def analyze_performance(self, hours=24):
        """Analyze performance metrics"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        response_times = []
        slow_requests = []
        
        with open(self.log_file_path, 'r') as f:
            for line in f:
                try:
                    log_entry = json.loads(line)
                    log_time = datetime.fromisoformat(log_entry['asctime'])
                    
                    if (log_time > cutoff_time and 
                        'duration' in log_entry and 
                        log_entry.get('message') == 'Request completed'):
                        
                        duration = float(log_entry['duration'])
                        response_times.append(duration)
                        
                        if duration > 2.0:  # Slow request threshold
                            slow_requests.append(log_entry)
                            
                except (json.JSONDecodeError, KeyError, ValueError):
                    continue
        
        if response_times:
            avg_response_time = sum(response_times) / len(response_times)
            p95_response_time = sorted(response_times)[int(len(response_times) * 0.95)]
        else:
            avg_response_time = 0
            p95_response_time = 0
        
        return {
            'total_requests': len(response_times),
            'avg_response_time': avg_response_time,
            'p95_response_time': p95_response_time,
            'slow_requests_count': len(slow_requests),
            'slow_requests': slow_requests[-10:]  # Last 10 slow requests
        }
    
    def generate_report(self):
        """Generate comprehensive log analysis report"""
        error_analysis = self.analyze_errors()
        performance_analysis = self.analyze_performance()
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'errors': error_analysis,
            'performance': performance_analysis,
            'alerts': []
        }
        
        # Generate alerts
        if error_analysis['total_errors'] > 100:
            report['alerts'].append({
                'type': 'high_error_rate',
                'message': f"High error rate: {error_analysis['total_errors']} errors in last 24h"
            })
        
        if performance_analysis['avg_response_time'] > 1.0:
            report['alerts'].append({
                'type': 'slow_response_time',
                'message': f"Slow average response time: {performance_analysis['avg_response_time']:.2f}s"
            })
        
        return report

# Automated log analysis task
@shared_task
def analyze_logs_and_alert():
    """Celery task to analyze logs and send alerts"""
    analyzer = LogAnalyzer('/var/log/crelancer/app.log')
    report = analyzer.generate_report()
    
    # Send alerts if any
    if report['alerts']:
        send_alert_notification.delay(report)
    
    # Store report for dashboard
    cache.set('log_analysis_report', report, 3600)
    
    return report
```

## 🗄️ Database Maintenance

### **Database Monitoring and Optimization**
```python
# backend/core/db_maintenance.py
from django.core.management.base import BaseCommand
from django.db import connection
import logging

logger = logging.getLogger(__name__)

class DatabaseMaintenance:
    @staticmethod
    def analyze_slow_queries():
        """Analyze slow queries from PostgreSQL logs"""
        with connection.cursor() as cursor:
            # Enable query logging if not already enabled
            cursor.execute("SELECT name, setting FROM pg_settings WHERE name LIKE 'log_%'")
            settings = dict(cursor.fetchall())
            
            # Get slow queries from pg_stat_statements
            cursor.execute("""
                SELECT query, calls, total_time, mean_time, rows
                FROM pg_stat_statements
                WHERE mean_time > 1000  -- Queries taking more than 1 second
                ORDER BY mean_time DESC
                LIMIT 20
            """)
            
            slow_queries = cursor.fetchall()
            
        return [
            {
                'query': query[:200] + '...' if len(query) > 200 else query,
                'calls': calls,
                'total_time': total_time,
                'mean_time': mean_time,
                'rows': rows
            }
            for query, calls, total_time, mean_time, rows in slow_queries
        ]
    
    @staticmethod
    def get_table_sizes():
        """Get table sizes for monitoring growth"""
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
                    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
                FROM pg_tables 
                WHERE schemaname = 'public'
                ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
            """)
            
            return [
                {
                    'schema': schema,
                    'table': table,
                    'size': size,
                    'size_bytes': size_bytes
                }
                for schema, table, size, size_bytes in cursor.fetchall()
            ]
    
    @staticmethod
    def vacuum_analyze():
        """Run VACUUM ANALYZE on all tables"""
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT tablename 
                FROM pg_tables 
                WHERE schemaname = 'public'
            """)
            
            tables = [row[0] for row in cursor.fetchall()]
            
            for table in tables:
                try:
                    cursor.execute(f'VACUUM ANALYZE "{table}"')
                    logger.info(f'VACUUM ANALYZE completed for table: {table}')
                except Exception as e:
                    logger.error(f'VACUUM ANALYZE failed for table {table}: {e}')

# Management command for database maintenance
class Command(BaseCommand):
    help = 'Perform database maintenance tasks'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--vacuum',
            action='store_true',
            help='Run VACUUM ANALYZE on all tables',
        )
        parser.add_argument(
            '--analyze-queries',
            action='store_true',
            help='Analyze slow queries',
        )
        parser.add_argument(
            '--table-sizes',
            action='store_true',
            help='Show table sizes',
        )
    
    def handle(self, *args, **options):
        maintenance = DatabaseMaintenance()
        
        if options['vacuum']:
            self.stdout.write('Running VACUUM ANALYZE...')
            maintenance.vacuum_analyze()
            self.stdout.write(self.style.SUCCESS('VACUUM ANALYZE completed'))
        
        if options['analyze_queries']:
            self.stdout.write('Analyzing slow queries...')
            slow_queries = maintenance.analyze_slow_queries()
            
            for query in slow_queries:
                self.stdout.write(f"Query: {query['query']}")
                self.stdout.write(f"  Calls: {query['calls']}, Mean time: {query['mean_time']:.2f}ms")
        
        if options['table_sizes']:
            self.stdout.write('Table sizes:')
            table_sizes = maintenance.get_table_sizes()
            
            for table in table_sizes:
                self.stdout.write(f"{table['table']}: {table['size']}")
```

## 💾 Backup and Disaster Recovery

### **Automated Backup System**
```bash
#!/bin/bash
# scripts/backup-database.sh

set -e

# Configuration
BACKUP_DIR="/backups/database"
RETENTION_DAYS=30
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="crelancer_backup_${TIMESTAMP}.sql"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

echo "🗄️ Starting database backup..."

# Create database backup
pg_dump $DATABASE_URL > "$BACKUP_DIR/$BACKUP_FILE"

# Compress backup
gzip "$BACKUP_DIR/$BACKUP_FILE"
BACKUP_FILE="${BACKUP_FILE}.gz"

echo "✅ Database backup created: $BACKUP_FILE"

# Upload to S3
if [ ! -z "$AWS_S3_BACKUP_BUCKET" ]; then
    echo "☁️ Uploading backup to S3..."
    aws s3 cp "$BACKUP_DIR/$BACKUP_FILE" "s3://$AWS_S3_BACKUP_BUCKET/database/"
    echo "✅ Backup uploaded to S3"
fi

# Clean up old backups
echo "🧹 Cleaning up old backups..."
find $BACKUP_DIR -name "crelancer_backup_*.sql.gz" -mtime +$RETENTION_DAYS -delete

echo "🎉 Backup process completed successfully!"

# Verify backup integrity
echo "🔍 Verifying backup integrity..."
if gunzip -t "$BACKUP_DIR/$BACKUP_FILE"; then
    echo "✅ Backup integrity verified"
else
    echo "❌ Backup integrity check failed!"
    exit 1
fi
```

### **Disaster Recovery Plan**
```bash
#!/bin/bash
# scripts/disaster-recovery.sh

set -e

echo "🚨 Starting disaster recovery process..."

# Configuration
BACKUP_BUCKET="s3://crelancer-backups"
RECOVERY_DB_NAME="crelancer_recovery"

# Function to restore from latest backup
restore_from_backup() {
    echo "📥 Downloading latest backup from S3..."
    
    # Get latest backup
    LATEST_BACKUP=$(aws s3 ls $BACKUP_BUCKET/database/ | sort | tail -n 1 | awk '{print $4}')
    
    if [ -z "$LATEST_BACKUP" ]; then
        echo "❌ No backup found in S3!"
        exit 1
    fi
    
    echo "📦 Latest backup: $LATEST_BACKUP"
    
    # Download backup
    aws s3 cp "$BACKUP_BUCKET/database/$LATEST_BACKUP" /tmp/
    
    # Extract backup
    gunzip "/tmp/$LATEST_BACKUP"
    BACKUP_FILE="/tmp/${LATEST_BACKUP%.gz}"
    
    # Create recovery database
    echo "🗄️ Creating recovery database..."
    createdb $RECOVERY_DB_NAME
    
    # Restore backup
    echo "🔄 Restoring backup..."
    psql $RECOVERY_DB_NAME < $BACKUP_FILE
    
    echo "✅ Database restored successfully!"
    
    # Clean up
    rm $BACKUP_FILE
}

# Function to switch to recovery database
switch_to_recovery() {
    echo "🔄 Switching to recovery database..."
    
    # Update environment variables
    export DATABASE_URL="********************************/$RECOVERY_DB_NAME"
    
    # Restart application services
    docker-compose restart web celery
    
    echo "✅ Switched to recovery database"
}

# Main recovery process
case "$1" in
    "restore")
        restore_from_backup
        ;;
    "switch")
        switch_to_recovery
        ;;
    "full")
        restore_from_backup
        switch_to_recovery
        ;;
    *)
        echo "Usage: $0 {restore|switch|full}"
        echo "  restore - Restore database from latest backup"
        echo "  switch  - Switch application to recovery database"
        echo "  full    - Restore and switch in one operation"
        exit 1
        ;;
esac

echo "🎉 Disaster recovery process completed!"
```

## 🎯 Key Takeaways

1. **Comprehensive Monitoring**: Application, infrastructure, and business metrics
2. **Error Tracking**: Structured logging and real-time error monitoring
3. **Performance Monitoring**: Response times, database queries, and resource usage
4. **Automated Maintenance**: Database optimization and cleanup tasks
5. **Disaster Recovery**: Automated backups and recovery procedures

## 🔗 What's Next?

Congratulations! You've completed the comprehensive Crelancer tutorial. You now have the knowledge to build, deploy, and maintain a production-ready freelance marketplace platform.

**Next Steps:**
- Review the [Project Summary](../07-conclusion/29-project-summary.md)
- Explore [Additional Resources](../07-conclusion/30-resources.md)
- Start building your own marketplace!
