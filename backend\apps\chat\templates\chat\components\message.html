{% load static %}

{# WARNING: YOU CAN NOT USE user VAR IN THIS TEMPLATE #}

<div class="chat-message" data-controller="chat-message" data-chat-message-sender-value="{{message.sender.id}}">
    <div class="message-wrap flex items-start">    
        {% if message.sender != None %}
            <div class="flex flex-col space-y-2 text-sm max-w-xs mx-2 order-2 items-start">
                <div class="avatar w-full flex flex-nowrap justify-between">
                    <img
                                src="{% if message.sender.avatar %}{{ message.sender.avatar.url }}{% else %}{% static 'vendors/images/default_avatar.png' %}{% endif %}"
                                alt="{{ message.sender.user.name_short }}"
                                class="w-10 h-10 rounded-full shadow order-1 object-cover">
                </div>
            </div>
        {% endif %}

        <div class="flex flex-col space-y-1 text-sm max-w-xs min-w-[240px] mx-2 order-2 items-start">
                <div class="message-body px-4 py-2 w-full rounded-lg font-medium break-words">
                    {% if message.template != None %}
                        {% include message.template with deal=message.deal %}
                    {% else %}
                        {{ message.text|urlize }}
                    {% endif %}
                </div>
                {% if message.attachments.all %}
                <div class="w-full">
                    <ul  class="space-y-2">
                        {% for attachment in message.attachments.all %}
                            <li>
                                <a href="{{ attachment.get_absolute_url }}" target="_blank" class="w-full block border border-downy-300 bg-white rounded-lg p-2 space-y-2">
                                    <div class="flex items-center justify-start gap-2">
                                        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" class="flex-grow-0 flex-shrink-0 w-7 h-7 relative" preserveAspectRatio="xMidYMid meet">
                                            <rect width="32" height="32" rx="16" fill="#F3FAFA"></rect>
                                            <path d="M16.6667 9.33325H12C11.6464 9.33325 11.3073 9.47373 11.0572 9.72378C10.8072 9.97382 10.6667 10.313 10.6667 10.6666V21.3333C10.6667 21.6869 10.8072 22.026 11.0572 22.2761C11.3073 22.5261 11.6464 22.6666 12 22.6666H20C20.3536 22.6666 20.6928 22.5261 20.9428 22.2761C21.1929 22.026 21.3334 21.6869 21.3334 21.3333V13.9999M16.6667 9.33325L21.3334 13.9999M16.6667 9.33325V13.9999H21.3334" stroke="#62C6C5" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"></path>
                                        </svg>
                                        <div class="font-medium truncate space-nowrap">{{ attachment }}</div>
                                    </div>
                                </a>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
                {% endif %}
            <div class="text-xs font-medium text-gray-500">{{ message.created_at }}</div>
        </div>
    </div>
</div>

