from django import forms
from django.conf import settings

from .models import Message


class MessageCreateForm(forms.ModelForm):
    attachments = forms.FileField(
        widget=forms.ClearableFileInput(attrs={"multiple": True}), required=False
    )

    class Meta:
        model = Message
        fields = ["text", "attachments"]

    def clean_attachments(self):
        data = self.cleaned_data["attachments"]

        files = self.files.getlist("attachments")
        allowed_count = settings.CHAT_MAX_ATTACHMENTS_COUNT
        if len(files) > allowed_count:
            raise forms.ValidationError(
                f"Too many attachments. Max {allowed_count} allowed."
            )

        for file in files:
            if file.size > settings.MESSAGE_ATTACHMENT_MAX_UPLOAD_SIZE:
                raise forms.ValidationError(
                    f"File {file.name} is too large. Max {settings.MESSAGE_ATTACHMENT_MAX_UPLOAD_SIZE} allowed."
                )

        return data
