# Lesson 21: Stripe Integration & Webhooks 💳

## 🎯 Learning Objectives
- Advanced Stripe Connect implementation
- Webhook handling and security
- Marketplace payment flows
- Error handling and edge cases
- Testing and debugging Stripe integration

## 🔗 Stripe Connect Architecture

### **Account Types and Flow**
```python
# Crelancer uses Stripe Connect Express accounts for talents
class StripeConnectService:
    @staticmethod
    def create_express_account(talent):
        """Create Stripe Express account for talent"""
        try:
            account = stripe.Account.create(
                type='express',
                country='US',
                email=talent.user.email,
                capabilities={
                    'card_payments': {'requested': True},
                    'transfers': {'requested': True},
                },
                business_type='individual',
                individual={
                    'first_name': talent.user.first_name,
                    'last_name': talent.user.last_name,
                    'email': talent.user.email,
                },
                settings={
                    'payouts': {
                        'schedule': {
                            'interval': 'weekly',
                            'weekly_anchor': 'friday'
                        }
                    }
                }
            )
            
            talent.stripe_account_id = account.id
            talent.stripe_account_data = {
                'account_id': account.id,
                'created': account.created,
                'country': account.country,
                'default_currency': account.default_currency
            }
            talent.save()
            
            return account
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create Stripe account for talent {talent.id}: {e}")
            raise
```

### **Onboarding Flow**
```python
class StripeOnboardingView(TalentRequiredMixin, View):
    def get(self, request):
        talent = request.user.talent
        
        # Create account if doesn't exist
        if not talent.stripe_account_id:
            StripeConnectService.create_express_account(talent)
        
        # Check account status
        account = stripe.Account.retrieve(talent.stripe_account_id)
        
        if not account.details_submitted:
            # Create onboarding link
            account_link = stripe.AccountLink.create(
                account=talent.stripe_account_id,
                refresh_url=request.build_absolute_uri(
                    reverse('talents:stripe_onboarding')
                ),
                return_url=request.build_absolute_uri(
                    reverse('talents:stripe_onboarding_complete')
                ),
                type='account_onboarding',
            )
            return redirect(account_link.url)
        
        elif not account.charges_enabled:
            return render(request, 'talents/stripe_pending.html', {
                'account': account
            })
        
        else:
            return redirect('talents:stripe_onboarding_complete')

class StripeOnboardingCompleteView(TalentRequiredMixin, TemplateView):
    template_name = 'talents/stripe_complete.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        talent = self.request.user.talent
        
        if talent.stripe_account_id:
            account = stripe.Account.retrieve(talent.stripe_account_id)
            context['account'] = account
            context['can_receive_payments'] = account.charges_enabled
            
            # Update talent verification status
            if account.charges_enabled and not talent.is_payment_verified:
                talent.is_payment_verified = True
                talent.save()
        
        return context
```

## 🔔 Webhook Implementation

### **Webhook Handler**
```python
import hmac
import hashlib
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

@method_decorator(csrf_exempt, name='dispatch')
class StripeWebhookView(View):
    def post(self, request):
        payload = request.body
        sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
        endpoint_secret = settings.STRIPE_WEBHOOK_SECRET
        
        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, endpoint_secret
            )
        except ValueError:
            logger.error("Invalid payload in Stripe webhook")
            return HttpResponse(status=400)
        except stripe.error.SignatureVerificationError:
            logger.error("Invalid signature in Stripe webhook")
            return HttpResponse(status=400)
        
        # Handle the event
        try:
            self.handle_webhook_event(event)
        except Exception as e:
            logger.error(f"Error handling webhook event {event['type']}: {e}")
            return HttpResponse(status=500)
        
        return HttpResponse(status=200)
    
    def handle_webhook_event(self, event):
        """Route webhook events to appropriate handlers"""
        event_type = event['type']
        event_data = event['data']['object']
        
        handlers = {
            'payment_intent.succeeded': self.handle_payment_succeeded,
            'payment_intent.payment_failed': self.handle_payment_failed,
            'account.updated': self.handle_account_updated,
            'transfer.created': self.handle_transfer_created,
            'transfer.failed': self.handle_transfer_failed,
            'payout.paid': self.handle_payout_paid,
            'payout.failed': self.handle_payout_failed,
            'charge.dispute.created': self.handle_dispute_created,
        }
        
        handler = handlers.get(event_type)
        if handler:
            handler(event_data, event)
        else:
            logger.info(f"Unhandled webhook event type: {event_type}")
    
    def handle_payment_succeeded(self, payment_intent, event):
        """Handle successful payment"""
        try:
            transaction = Transaction.objects.get(
                stripe_payment_intent_id=payment_intent['id']
            )
            
            if transaction.status != Transaction.Status.COMPLETED:
                transaction.status = Transaction.Status.COMPLETED
                transaction.stripe_charge_id = payment_intent.get('latest_charge')
                transaction.processed_at = timezone.now()
                transaction.completed_at = timezone.now()
                transaction.save()
                
                # Update milestone
                if transaction.milestone:
                    transaction.milestone.status = Milestone.StatusChoices.PAID
                    transaction.milestone.save()
                
                # Process talent payout
                self.process_talent_payout(transaction)
                
                # Send notifications
                send_payment_success_notification.delay(transaction.id)
                
        except Transaction.DoesNotExist:
            logger.error(f"Transaction not found for payment intent: {payment_intent['id']}")
    
    def handle_payment_failed(self, payment_intent, event):
        """Handle failed payment"""
        try:
            transaction = Transaction.objects.get(
                stripe_payment_intent_id=payment_intent['id']
            )
            
            transaction.status = Transaction.Status.FAILED
            transaction.save()
            
            # Send failure notification
            send_payment_failure_notification.delay(transaction.id)
            
        except Transaction.DoesNotExist:
            logger.error(f"Transaction not found for failed payment: {payment_intent['id']}")
    
    def handle_account_updated(self, account, event):
        """Handle Stripe account updates"""
        try:
            talent = Talent.objects.get(stripe_account_id=account['id'])
            
            # Update account data
            talent.stripe_account_data.update({
                'charges_enabled': account.get('charges_enabled', False),
                'payouts_enabled': account.get('payouts_enabled', False),
                'details_submitted': account.get('details_submitted', False),
            })
            talent.save()
            
            # Update verification status
            if account.get('charges_enabled') and not talent.is_payment_verified:
                talent.is_payment_verified = True
                talent.save()
                
                # Send verification complete notification
                send_payment_verification_complete.delay(talent.id)
                
        except Talent.DoesNotExist:
            logger.error(f"Talent not found for Stripe account: {account['id']}")
```

## 💰 Advanced Payment Flows

### **Escrow System**
```python
class EscrowService:
    @staticmethod
    def create_escrow_payment(milestone):
        """Create payment intent with funds held in escrow"""
        deal = milestone.deal
        client = deal.client
        
        # Calculate amounts
        gross_amount = milestone.amount
        platform_fee = gross_amount * Decimal('0.05')  # 5% platform fee
        stripe_fee = gross_amount * Decimal('0.029') + Decimal('0.30')  # Stripe fees
        net_amount = gross_amount - platform_fee - stripe_fee
        
        try:
            # Create payment intent with manual capture
            intent = stripe.PaymentIntent.create(
                amount=int(gross_amount * 100),  # Convert to cents
                currency='usd',
                customer=client.stripe_customer_id,
                description=f"Escrow payment for: {milestone.title}",
                metadata={
                    'deal_id': deal.id,
                    'milestone_id': milestone.id,
                    'platform_fee': str(platform_fee),
                    'net_amount': str(net_amount)
                },
                capture_method='manual',  # Hold funds until milestone completion
                transfer_group=f"deal_{deal.id}"
            )
            
            # Create transaction record
            transaction = Transaction.objects.create(
                deal=deal,
                milestone=milestone,
                transaction_type=Transaction.TransactionType.PAYMENT,
                amount=gross_amount,
                fee_amount=platform_fee + stripe_fee,
                net_amount=net_amount,
                status=Transaction.Status.PENDING,
                stripe_payment_intent_id=intent.id,
                description=f"Escrow payment for: {milestone.title}"
            )
            
            return intent, transaction
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create escrow payment: {e}")
            raise PaymentError(f"Failed to create payment: {str(e)}")
    
    @staticmethod
    def release_escrow_payment(transaction):
        """Release escrowed funds to talent"""
        try:
            # Capture the payment
            intent = stripe.PaymentIntent.capture(
                transaction.stripe_payment_intent_id
            )
            
            # Transfer to talent
            talent = transaction.deal.talent
            transfer = stripe.Transfer.create(
                amount=int(transaction.net_amount * 100),
                currency='usd',
                destination=talent.stripe_account_id,
                transfer_group=f"deal_{transaction.deal.id}",
                description=f"Payment for: {transaction.milestone.title}"
            )
            
            # Update transaction
            transaction.status = Transaction.Status.COMPLETED
            transaction.stripe_transfer_id = transfer.id
            transaction.completed_at = timezone.now()
            transaction.save()
            
            # Create payout record
            Transaction.objects.create(
                deal=transaction.deal,
                milestone=transaction.milestone,
                transaction_type=Transaction.TransactionType.PAYOUT,
                amount=transaction.net_amount,
                fee_amount=0,
                net_amount=transaction.net_amount,
                status=Transaction.Status.COMPLETED,
                stripe_transfer_id=transfer.id,
                completed_at=timezone.now(),
                description=f"Payout for: {transaction.milestone.title}"
            )
            
            return transfer
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to release escrow payment: {e}")
            raise PaymentError(f"Failed to release payment: {str(e)}")
```

### **Refund Processing**
```python
class RefundService:
    @staticmethod
    def process_refund(transaction, amount=None, reason="requested_by_customer"):
        """Process refund for a transaction"""
        if transaction.status != Transaction.Status.COMPLETED:
            raise PaymentError("Can only refund completed transactions")
        
        refund_amount = amount or transaction.amount
        
        try:
            # Create Stripe refund
            refund = stripe.Refund.create(
                payment_intent=transaction.stripe_payment_intent_id,
                amount=int(refund_amount * 100),
                reason=reason,
                metadata={
                    'original_transaction_id': transaction.id,
                    'deal_id': transaction.deal.id
                }
            )
            
            # Create refund transaction record
            refund_transaction = Transaction.objects.create(
                deal=transaction.deal,
                milestone=transaction.milestone,
                transaction_type=Transaction.TransactionType.REFUND,
                amount=refund_amount,
                fee_amount=0,
                net_amount=refund_amount,
                status=Transaction.Status.COMPLETED,
                stripe_charge_id=refund.id,
                completed_at=timezone.now(),
                description=f"Refund for transaction {transaction.id}"
            )
            
            # If talent was already paid, reverse the transfer
            if transaction.stripe_transfer_id:
                try:
                    reversal = stripe.Transfer.create_reversal(
                        transaction.stripe_transfer_id,
                        amount=int(refund_amount * 100)
                    )
                    
                    # Create reversal record
                    Transaction.objects.create(
                        deal=transaction.deal,
                        milestone=transaction.milestone,
                        transaction_type=Transaction.TransactionType.CHARGEBACK,
                        amount=refund_amount,
                        fee_amount=0,
                        net_amount=-refund_amount,  # Negative for talent
                        status=Transaction.Status.COMPLETED,
                        stripe_transfer_id=reversal.id,
                        completed_at=timezone.now(),
                        description=f"Transfer reversal for refund {refund.id}"
                    )
                    
                except stripe.error.StripeError as e:
                    logger.error(f"Failed to reverse transfer: {e}")
                    # Handle case where reversal fails
            
            # Send notifications
            send_refund_notification.delay(refund_transaction.id)
            
            return refund_transaction
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to process refund: {e}")
            raise PaymentError(f"Failed to process refund: {str(e)}")
```

## 🧪 Testing Stripe Integration

### **Test Data Setup**
```python
class StripeTestCase(TestCase):
    def setUp(self):
        # Use Stripe test keys
        stripe.api_key = settings.STRIPE_TEST_SECRET_KEY
        
        # Create test customer
        self.test_customer = stripe.Customer.create(
            email="<EMAIL>",
            name="Test Client"
        )
        
        # Create test connected account
        self.test_account = stripe.Account.create(
            type='express',
            country='US',
            email='<EMAIL>'
        )
        
        # Create test payment method
        self.test_payment_method = stripe.PaymentMethod.create(
            type="card",
            card={
                "number": "****************",
                "exp_month": 12,
                "exp_year": 2025,
                "cvc": "123",
            },
        )
    
    def test_payment_intent_creation(self):
        """Test creating payment intent"""
        milestone = MilestoneFactory()
        
        intent, transaction = EscrowService.create_escrow_payment(milestone)
        
        self.assertIsNotNone(intent.id)
        self.assertEqual(transaction.status, Transaction.Status.PENDING)
        self.assertEqual(transaction.stripe_payment_intent_id, intent.id)
    
    def test_webhook_payment_succeeded(self):
        """Test webhook handling for successful payment"""
        transaction = TransactionFactory(
            stripe_payment_intent_id="pi_test_123",
            status=Transaction.Status.PENDING
        )
        
        # Simulate webhook event
        event_data = {
            'id': 'pi_test_123',
            'status': 'succeeded',
            'latest_charge': 'ch_test_123'
        }
        
        webhook_view = StripeWebhookView()
        webhook_view.handle_payment_succeeded(event_data, {})
        
        transaction.refresh_from_db()
        self.assertEqual(transaction.status, Transaction.Status.COMPLETED)
```

## 🎯 Key Takeaways

1. **Stripe Connect**: Express accounts for marketplace payments
2. **Webhook Security**: Proper signature verification and error handling
3. **Escrow System**: Hold funds until milestone completion
4. **Comprehensive Testing**: Test all payment flows and edge cases
5. **Error Handling**: Graceful handling of Stripe API errors

## 🔗 What's Next?

**Next**: [Lesson 22: File Storage & Media Management](./22-file-storage.md)
