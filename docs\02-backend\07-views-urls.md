# Lesson 7: Views & URL Patterns 🛣️

## 🎯 Learning Objectives
By the end of this lesson, you will understand:
- URL routing and organization in Crelancer
- Different types of views and their use cases
- Permission systems and access control
- Form handling and validation
- Hotwire integration with Django views

## 🛣️ URL Structure and Organization

### **Main URL Configuration**
```python
# backend/urls.py
from django.urls import path, include
from django.contrib import admin

urlpatterns = [
    # Authentication and user management
    path("", include("registration.urls")),
    
    # User profiles
    path("crelancers/", include("talents.urls")),
    path("clients/", include("clients.urls")),
    
    # Core business features
    path("jobs/", include("jobs.urls")),
    path("deals/", include("deals.urls")),
    path("chat/", include("chat.urls")),
    
    # Supporting features
    path("portfolio/", include("portfolio.urls")),
    path("finance/", include("finance.urls")),
    path("reviews/", include("reviews.urls")),
    path("disputes/", include("disputes.urls")),
    
    # Admin and utilities
    path("admin/", admin.site.urls),
    path("tags_input/", include("tags_input.urls", namespace="tags_input")),
]
```

### **App-Level URL Patterns**
```python
# Example: jobs/urls.py
from django.urls import path, include
from . import views

app_name = "jobs"
urlpatterns = [
    # Job listing and search
    path("", views.JobListView.as_view(), name="list"),
    path("search/", views.JobSearchView.as_view(), name="search"),
    
    # Job management
    path("create/", views.JobCreateView.as_view(), name="create"),
    path("<int:pk>/", views.JobDetailView.as_view(), name="detail"),
    path("<int:pk>/edit/", views.JobUpdateView.as_view(), name="edit"),
    path("<int:pk>/delete/", views.JobDeleteView.as_view(), name="delete"),
    
    # Job actions
    path("<int:pk>/publish/", views.JobPublishView.as_view(), name="publish"),
    path("<int:pk>/applications/", views.JobApplicationsView.as_view(), name="applications"),
    
    # API endpoints for AJAX
    path("api/", include([
        path("skills/", views.SkillAutocompleteView.as_view(), name="skill_autocomplete"),
        path("<int:pk>/favorite/", views.JobFavoriteToggleView.as_view(), name="favorite_toggle"),
    ])),
]
```

## 🎭 View Types and Patterns

### **Class-Based Views (CBVs)**

#### **List Views**
```python
class JobListView(ListView):
    """
    Display paginated list of published jobs
    """
    model = Job
    template_name = "jobs/job_list.html"
    context_object_name = "jobs"
    paginate_by = 20
    
    def get_queryset(self):
        return Job.objects.published().select_related(
            'client__user', 'category'
        ).prefetch_related('skills')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Category.objects.all()
        context['featured_jobs'] = Job.objects.published()[:5]
        return context

class TalentListView(ListView):
    """
    Display verified talents with filtering
    """
    model = Talent
    template_name = "talents/talent_list.html"
    context_object_name = "talents"
    paginate_by = 12
    
    def get_queryset(self):
        queryset = Talent.objects.available().select_related('user')
        
        # Filter by category
        category = self.request.GET.get('category')
        if category:
            queryset = queryset.filter(categories__slug=category)
        
        # Filter by skills
        skills = self.request.GET.getlist('skills')
        if skills:
            queryset = queryset.filter(skills__id__in=skills).distinct()
        
        return queryset
```

#### **Detail Views**
```python
class JobDetailView(DetailView):
    """
    Display job details with application form for talents
    """
    model = Job
    template_name = "jobs/job_detail.html"
    context_object_name = "job"
    
    def get_queryset(self):
        return Job.objects.published().select_related(
            'client__user', 'category'
        ).prefetch_related('skills')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Add related jobs
        context['related_jobs'] = Job.objects.published().filter(
            category=self.object.category
        ).exclude(pk=self.object.pk)[:4]
        
        # Check if user already applied
        if (self.request.user.is_authenticated and 
            hasattr(self.request.user, 'talent')):
            context['has_applied'] = Deal.objects.filter(
                job=self.object,
                talent=self.request.user.talent
            ).exists()
        
        return context

class TalentDetailView(DetailView):
    """
    Display talent profile with portfolio and reviews
    """
    model = Talent
    template_name = "talents/talent_detail.html"
    context_object_name = "talent"
    
    def get_queryset(self):
        return Talent.objects.published().select_related('user')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['portfolio_projects'] = self.object.projects.all()[:6]
        context['recent_reviews'] = self.object.received_reviews.all()[:5]
        return context
```

#### **Form Views**
```python
class JobCreateView(ClientRequiredMixin, CreateView):
    """
    Create new job posting
    """
    model = Job
    form_class = JobForm
    template_name = "jobs/job_form.html"
    
    def form_valid(self, form):
        form.instance.client = self.request.user.client
        response = super().form_valid(form)
        
        messages.success(
            self.request, 
            "Job created successfully! You can now publish it."
        )
        return response
    
    def get_success_url(self):
        return reverse('jobs:detail', kwargs={'pk': self.object.pk})

class TalentProfileUpdateView(TalentRequiredMixin, UpdateView):
    """
    Update talent profile
    """
    model = Talent
    form_class = TalentProfileForm
    template_name = "talents/profile_form.html"
    
    def get_object(self):
        return self.request.user.talent
    
    def form_valid(self, form):
        response = super().form_valid(form)
        
        # Check if profile is now complete
        if self.object.is_profile_complete():
            self.object.is_published = True
            self.object.save()
            messages.success(
                self.request,
                "Profile updated and published successfully!"
            )
        
        return response
```

### **Function-Based Views**
```python
@login_required
@require_http_methods(["POST"])
def job_favorite_toggle(request, pk):
    """
    Toggle job favorite status via AJAX
    """
    job = get_object_or_404(Job, pk=pk)
    
    if hasattr(request.user, 'talent'):
        favorite, created = JobFavorite.objects.get_or_create(
            job=job,
            talent=request.user.talent
        )
        
        if not created:
            favorite.delete()
            is_favorited = False
        else:
            is_favorited = True
        
        return JsonResponse({
            'is_favorited': is_favorited,
            'favorites_count': job.favorites.count()
        })
    
    return JsonResponse({'error': 'Unauthorized'}, status=403)

@talent_required
def job_application_create(request, job_id):
    """
    Create job application (deal)
    """
    job = get_object_or_404(Job, pk=job_id, status=Job.StatusChoices.PUBLISHED)
    
    # Check if already applied
    existing_deal = Deal.objects.filter(
        job=job,
        talent=request.user.talent
    ).first()
    
    if existing_deal:
        return redirect('chat:message_list', deal_id=existing_deal.id)
    
    if request.method == 'POST':
        form = JobApplicationForm(request.POST)
        if form.is_valid():
            deal = form.save(commit=False)
            deal.job = job
            deal.talent = request.user.talent
            deal.client = job.client
            deal.save()
            
            # Create initial system message
            Message.objects.create(
                deal=deal,
                sender=request.user,
                content=f"Applied for job: {job.title}",
                message_type=Message.MessageType.SYSTEM
            )
            
            messages.success(request, "Application submitted successfully!")
            return redirect('chat:message_list', deal_id=deal.id)
    else:
        form = JobApplicationForm()
    
    return render(request, 'jobs/application_form.html', {
        'form': form,
        'job': job
    })
```

## 🔐 Permission System

### **Custom Permission Mixins**
```python
from django.contrib.auth.mixins import UserPassesTestMixin
from django.core.exceptions import PermissionDenied

class TalentRequiredMixin(UserPassesTestMixin):
    """
    Require user to be authenticated talent
    """
    def test_func(self):
        return (
            self.request.user.is_authenticated and
            self.request.user.role == User.RoleChoices.TALENT and
            hasattr(self.request.user, 'talent')
        )
    
    def handle_no_permission(self):
        if not self.request.user.is_authenticated:
            return redirect('login')
        raise PermissionDenied("You must be a talent to access this page.")

class ClientRequiredMixin(UserPassesTestMixin):
    """
    Require user to be authenticated client
    """
    def test_func(self):
        return (
            self.request.user.is_authenticated and
            self.request.user.role == User.RoleChoices.CLIENT and
            hasattr(self.request.user, 'client')
        )

class VerifiedTalentRequiredMixin(TalentRequiredMixin):
    """
    Require user to be verified talent
    """
    def test_func(self):
        return (
            super().test_func() and
            self.request.user.talent.is_verified
        )

class DealParticipantMixin(UserPassesTestMixin):
    """
    Require user to be participant in the deal
    """
    def test_func(self):
        deal = self.get_object()
        return (
            self.request.user == deal.client.user or
            self.request.user == deal.talent.user
        )
```

### **Decorators for Function Views**
```python
from functools import wraps

def talent_required(view_func):
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('login')
        
        if (request.user.role != User.RoleChoices.TALENT or
            not hasattr(request.user, 'talent')):
            raise PermissionDenied
        
        return view_func(request, *args, **kwargs)
    return _wrapped_view

def client_required(view_func):
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            return redirect('login')
        
        if (request.user.role != User.RoleChoices.CLIENT or
            not hasattr(request.user, 'client')):
            raise PermissionDenied
        
        return view_func(request, *args, **kwargs)
    return _wrapped_view

# Usage
@talent_required
def talent_dashboard(request):
    talent = request.user.talent
    active_deals = Deal.objects.filter(
        talent=talent,
        status=Deal.StatusChoices.ACTIVE
    )
    return render(request, 'talents/dashboard.html', {
        'active_deals': active_deals
    })
```

## 🔄 Hotwire Integration

### **Turbo Frame Views**
```python
class JobApplicationsFrameView(ClientRequiredMixin, TemplateView):
    """
    Turbo Frame for job applications list
    """
    template_name = "jobs/applications_frame.html"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        job = get_object_or_404(Job, pk=kwargs['job_id'])
        
        # Ensure user owns the job
        if job.client != self.request.user.client:
            raise PermissionDenied
        
        context['job'] = job
        context['applications'] = Deal.objects.filter(
            job=job,
            status=Deal.StatusChoices.PENDING
        ).select_related('talent__user')
        
        return context

class DealStatusUpdateView(DealParticipantMixin, View):
    """
    Update deal status via Turbo Stream
    """
    http_method_names = ['post']
    
    def post(self, request, *args, **kwargs):
        deal = self.get_object()
        action = request.POST.get('action')
        
        if action == 'accept' and request.user == deal.client.user:
            deal.hire()
            
            # Return Turbo Stream response
            return TurboStream("deal-status").replace.response(
                "deals/status_widget.html",
                {"deal": deal}
            )
        
        elif action == 'reject' and request.user == deal.client.user:
            deal.status = Deal.StatusChoices.CANCELLED
            deal.save()
            
            return TurboStream("deal-status").replace.response(
                "deals/status_widget.html",
                {"deal": deal}
            )
        
        return HttpResponseBadRequest()
```

### **Search and Filtering Views**
```python
class JobSearchView(ListView):
    """
    AJAX-powered job search with filters
    """
    model = Job
    template_name = "jobs/search_results.html"
    context_object_name = "jobs"
    paginate_by = 10
    
    def get_queryset(self):
        queryset = Job.objects.published()
        
        # Search query
        q = self.request.GET.get('q')
        if q:
            queryset = queryset.filter(
                Q(title__icontains=q) | Q(description__icontains=q)
            )
        
        # Category filter
        category = self.request.GET.get('category')
        if category:
            queryset = queryset.filter(category__slug=category)
        
        # Skills filter
        skills = self.request.GET.getlist('skills')
        if skills:
            queryset = queryset.filter(skills__id__in=skills).distinct()
        
        # Budget filter
        min_budget = self.request.GET.get('min_budget')
        max_budget = self.request.GET.get('max_budget')
        if min_budget:
            queryset = queryset.filter(budget_min__gte=min_budget)
        if max_budget:
            queryset = queryset.filter(budget_max__lte=max_budget)
        
        return queryset.select_related('client__user', 'category')
    
    def get_template_names(self):
        if self.request.headers.get('HX-Request'):
            return ['jobs/search_results_frame.html']
        return super().get_template_names()
```

## 📝 Form Handling

### **Custom Form Views**
```python
class TalentVerificationView(TalentRequiredMixin, FormView):
    """
    Handle talent verification process
    """
    form_class = TalentVerificationForm
    template_name = "talents/verification_form.html"
    
    def get_initial(self):
        return {
            'talent': self.request.user.talent
        }
    
    def form_valid(self, form):
        # Process verification documents
        verification = form.save(commit=False)
        verification.talent = self.request.user.talent
        verification.status = 'pending'
        verification.save()
        
        # Send notification to admin
        send_verification_notification.delay(verification.id)
        
        messages.success(
            self.request,
            "Verification documents submitted successfully! "
            "We'll review them within 24 hours."
        )
        
        return redirect('talents:profile')

class MultiStepJobCreateView(ClientRequiredMixin, SessionWizardView):
    """
    Multi-step job creation wizard
    """
    template_name = "jobs/create_wizard.html"
    form_list = [
        ('basic', JobBasicInfoForm),
        ('details', JobDetailsForm),
        ('budget', JobBudgetForm),
        ('review', JobReviewForm),
    ]
    
    def done(self, form_list, **kwargs):
        # Combine data from all forms
        job_data = {}
        for form in form_list:
            job_data.update(form.cleaned_data)
        
        # Create job
        job = Job.objects.create(
            client=self.request.user.client,
            **job_data
        )
        
        messages.success(
            self.request,
            f"Job '{job.title}' created successfully!"
        )
        
        return redirect('jobs:detail', pk=job.pk)
```

## 🎯 Key Takeaways

1. **URL Organization**: Clear hierarchy with app namespaces
2. **View Patterns**: Mix of CBVs and FBVs based on complexity
3. **Permission System**: Role-based access control with custom mixins
4. **Hotwire Integration**: Turbo Frames and Streams for dynamic updates
5. **Form Handling**: Complex forms with validation and multi-step wizards
6. **Performance**: Optimized queries with select_related and prefetch_related

## 🔗 What's Next?

Now that you understand views and URLs, let's explore how templates work with Hotwire to create dynamic user interfaces.

**Next**: [Lesson 8: Templates & Hotwire Integration](./08-templates-hotwire.md)

---

## 💡 Quick Quiz

1. What's the purpose of app_name in URL configuration?
2. How do permission mixins work in class-based views?
3. What's the difference between Turbo Frame and Turbo Stream responses?
4. How are AJAX requests handled differently in views?

*Answers: 1) Creates namespace for URL reversing, 2) test_func() method checks permissions before view execution, 3) Frames replace content, Streams append/prepend/replace specific elements, 4) Different templates returned based on HX-Request header*
