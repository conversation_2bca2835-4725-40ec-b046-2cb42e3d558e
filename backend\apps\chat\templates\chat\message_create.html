<turbo-frame id="chat-messages-create">
    <form method="post" action="{% url "chat:message_create" deal.id %}" enctype="multipart/form-data" class="relative">
        {% csrf_token %}

        <div class="mb-2">
            {# {{ form.text }}#}
            <textarea name="{{ form.text.name }}" id="{{ form.text.id_for_label }}" rows="3" data-chat-target="textarea" class="bg-gray-50 border border-gray-300 text-gray-900 sm:text-sm !rounded-[20px] focus:ring-downy-300 focus:border-downy-300 block w-full px-[48px] py-2.5 sm:py-3.5 bg-white appearance-none border leading-normal border-gray-300 rounded-lg px-4 py-2 w-full block text-gray-700 focus:outline-none resize-none">{% if form.text.value != None %}{{ form.text.value }}{% endif %}</textarea>
            {% for error in form.text.errors %}
                <div class="text-xs font-medium text-red-600">{{ error }}</div>
            {% endfor %}
        </div>
        <div class="flex justify-end items-center space-x-4">
            <div class="w-full">
                <div data-chat-target="fileList" id="files-names" class="flex flex-col sm:flex-row sm:flex-wrap sm:gap-x-4 mt-2">                                     
                </div>
            </div>
            <div class="absolute w-6 h-6 top-3 left-0">
                {# {{ form.attachments }}#}
                <label for="{{ form.attachments.id_for_label }}" class="absolute top-0 left-0 w-5 h-5 z-[2] cursor-pointer">
                    <svg width="20" height="22" viewBox="0 0 20 22" fill="none" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none" class="shrink-0">
                        <path d="M19.1527 9.89945L10.1371 18.9151C8.08686 20.9653 4.76275 20.9653 2.71249 18.9151C0.662241 16.8648 0.662242 13.5407 2.71249 11.4904L11.7281 2.47483C13.0949 1.108 15.311 1.108 16.6779 2.47483C18.0447 3.84167 18.0447 6.05775 16.6779 7.42458L8.01579 16.0866C7.33238 16.7701 6.22434 16.7701 5.54092 16.0866C4.8575 15.4032 4.8575 14.2952 5.54092 13.6118L13.1423 6.01037" stroke="#62C6C5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                </label>
                <input name="{{ form.attachments.name }}" id="{{ form.attachments.id_for_label }}" type="file" multiple data-chat-target="inputFile" data-action="change->chat#handleFileChange" class="!w-[1px] h-[1px] !hidden opacity-0">
            </div>
            <input type="submit" value="Send" data-action="click->scroll-to-down#scrollToDown click->chat#validateTextarea" class="w-8 h-8 absolute top-3 right-3 bg-icon-send bg-no-repeat bg-center text-transparent">
        </div>
        {% for error in form.attachments.errors %}
            <div class="text-xs font-medium text-red-600">{{ error }}</div>
        {% endfor %}
        <div>            
        </div>        
    </form>
</turbo-frame>