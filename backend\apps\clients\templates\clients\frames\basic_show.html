<turbo-frame id="profile_basic">
    <div class="relative">
        <a href="{% url "clients:basic_change" %}" class="absolute -top-1 right-0 w-8 h-8 flex justify-between items-center text-downy-300">
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-8 h-8" preserveAspectRatio="xMidYMid meet">
                <g clip-path="url(#clip0_1130_14216)">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.4707 20.3497V23.5293H11.6503L20.8523 14.2434L17.6738 11.0628L8.4707 20.3497ZM23.2789 11.8168C23.3582 11.7412 23.4214 11.6504 23.4645 11.5497C23.5076 11.449 23.5299 11.3406 23.5299 11.2311C23.5299 11.1216 23.5076 11.0132 23.4645 10.9125C23.4214 10.8118 23.3582 10.721 23.2789 10.6454L21.3535 8.72111C21.278 8.64197 21.1872 8.57898 21.0866 8.53594C20.986 8.49289 20.8778 8.4707 20.7684 8.4707C20.659 8.4707 20.5507 8.49289 20.4501 8.53594C20.3496 8.57898 20.2588 8.64197 20.1832 8.72111L18.6774 10.227L21.8558 13.4066L23.2789 11.8168Z" fill="currentColor"></path>
                </g>
                <rect x="0.5" y="0.5" width="31" height="31" rx="15.5" stroke="currentColor"></rect>
                <defs>
                  <clipPath id="clip0_1130_14216">
                    <rect width="15.0588" height="15.0588" fill="currentColor" transform="translate(8.4707 8.4707)"></rect>
                  </clipPath>
                </defs>
            </svg>
        </a>

        <div class="mb-4 pr-10">
            <h3 class="font-semibold mb-1">Website</h3>
            {% if request.user.client.website %}
            <div>
                <a href="{{ request.user.client.website }}" data-turbo="false" target="_blank" class="font-medium text-purple-900 break-words">
                    {{ request.user.client.website }}
                </a>
            </div>
            {% else %}
            <p>None</p>
            {% endif %}
        </div>

        <div class="mb-4 sm:pr-10">
            <h3 class="font-semibold mb-1">About</h3>
            {% if request.user.client.about%}
            <div class="text-gray-500 font-medium">
                {{ request.user.client.about }}
            </div>
            {% else %}
            <p>None</p>
            {% endif %}
        </div>
    </div>   
</turbo-frame>