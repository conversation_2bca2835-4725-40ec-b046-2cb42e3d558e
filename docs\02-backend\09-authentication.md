# Lesson 9: Authentication & User Management 🔐

## 🎯 Learning Objectives
By the end of this lesson, you will understand:
- Custom user model and authentication system
- Registration and email verification process
- Role-based access control
- Password management and security
- Session management and user preferences

## 👤 Custom User Model

### **User Model Design**
```python
# backend/apps/registration/models.py
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _

class User(AbstractUser, UnRemovableModel):
    """
    Custom user model with email authentication and role-based access
    """
    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = ["first_name", "last_name"]
    
    class RoleChoices(models.TextChoices):
        TALENT = "talent", "Talent"
        CLIENT = "client", "Client"
    
    # Remove username field, use email instead
    username = None
    email = LowercaseEmailField(_("email address"), unique=True)
    
    # User role and profile completion
    role = models.CharField(max_length=10, choices=RoleChoices.choices)
    is_email_confirmed = models.BooleanField(default=False)
    
    # Profile information
    phone = models.CharField(max_length=20, blank=True)
    timezone = models.CharField(max_length=50, default='UTC')
    
    # Soft delete functionality
    is_removed = models.BooleanField(default=False)
    removed_at = models.DateTimeField(null=True, blank=True)
    
    # Timestamps
    date_joined = models.DateTimeField(auto_now_add=True)
    last_login = models.DateTimeField(null=True, blank=True)
    
    objects = UserManager()
    
    class Meta:
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['role', 'is_active']),
            models.Index(fields=['is_email_confirmed']),
        ]
    
    def get_profile(self):
        """Get the appropriate profile based on user role"""
        if self.role == self.RoleChoices.TALENT:
            return getattr(self, 'talent', None)
        elif self.role == self.RoleChoices.CLIENT:
            return getattr(self, 'client', None)
        return None
    
    def get_full_name(self):
        """Return the first_name plus the last_name, with a space in between."""
        full_name = f"{self.first_name} {self.last_name}"
        return full_name.strip()
    
    def can_access_admin(self):
        """Check if user can access admin panel"""
        return self.is_staff or self.is_superuser
    
    def is_profile_complete(self):
        """Check if user has completed their profile"""
        profile = self.get_profile()
        if profile:
            return profile.is_profile_complete()
        return False
```

### **Custom User Manager**
```python
from django.contrib.auth.models import BaseUserManager

class UserManager(BaseUserManager):
    """
    Custom user manager for email-based authentication
    """
    
    def create_user(self, email, password=None, **extra_fields):
        """Create and return a regular user with an email and password."""
        if not email:
            raise ValueError('The Email field must be set')
        
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user
    
    def create_superuser(self, email, password=None, **extra_fields):
        """Create and return a superuser with an email and password."""
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_email_confirmed', True)
        extra_fields.setdefault('role', User.RoleChoices.CLIENT)
        
        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')
        
        return self.create_user(email, password, **extra_fields)
    
    def active(self):
        """Return only active users"""
        return self.filter(is_active=True, is_removed=False)
    
    def talents(self):
        """Return only talent users"""
        return self.filter(role=User.RoleChoices.TALENT)
    
    def clients(self):
        """Return only client users"""
        return self.filter(role=User.RoleChoices.CLIENT)
```

## 📝 Registration System

### **Registration Views**
```python
# backend/apps/registration/views.py
from django.contrib.auth import login
from django.contrib.auth.views import LoginView
from django.views.generic import FormView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages

class SignupView(FormView):
    """
    Handle user registration with role selection
    """
    template_name = "registration/signup.html"
    form_class = SignupForm
    success_url = reverse_lazy("signup_incomplete")
    
    def get_template_names(self):
        # Show role selection if no role specified
        if (self.request.method == "GET" and 
            self.request.GET.get("role", "") not in User.RoleChoices.values):
            return ["registration/signup_role.html"]
        return super().get_template_names()
    
    def get_form_class(self):
        # Use different forms for different roles
        role = self.request.GET.get("role") or self.request.POST.get("role")
        if role == "client":
            return ClientSignupForm
        return TalentSignupForm
    
    def form_valid(self, form):
        # Create user but don't activate yet
        user = form.save(commit=False)
        user.is_active = False
        user.save()
        
        # Create profile based on role
        if user.role == User.RoleChoices.TALENT:
            Talent.objects.create(user=user)
        elif user.role == User.RoleChoices.CLIENT:
            Client.objects.create(user=user)
        
        # Send confirmation email
        self.send_confirmation_email(user)
        
        messages.success(
            self.request,
            "Account created! Please check your email to confirm your account."
        )
        
        return super().form_valid(form)
    
    def send_confirmation_email(self, user):
        """Send email confirmation link"""
        token = account_activation_token.make_token(user)
        uid = urlsafe_base64_encode(force_bytes(user.pk))
        
        confirmation_url = self.request.build_absolute_uri(
            reverse('signup_confirm', kwargs={'uidb64': uid, 'token': token})
        )
        
        send_mail(
            subject='Confirm your Crelancer account',
            message=f'Please click the link to confirm your account: {confirmation_url}',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            fail_silently=False,
        )

class SignupConfirmView(TemplateView):
    """
    Handle email confirmation
    """
    template_name = "registration/signup_confirm.html"
    
    def get(self, request, *args, **kwargs):
        uidb64 = kwargs.get('uidb64')
        token = kwargs.get('token')
        
        try:
            uid = force_str(urlsafe_base64_decode(uidb64))
            user = User.objects.get(pk=uid)
        except (TypeError, ValueError, OverflowError, User.DoesNotExist):
            user = None
        
        if user and account_activation_token.check_token(user, token):
            user.is_active = True
            user.is_email_confirmed = True
            user.save()
            
            # Auto-login the user
            login(request, user)
            
            messages.success(
                request,
                "Your account has been confirmed! Welcome to Crelancer."
            )
            
            # Redirect to profile completion
            return redirect(self.get_success_url(user))
        else:
            messages.error(
                request,
                "The confirmation link is invalid or has expired."
            )
        
        return super().get(request, *args, **kwargs)
    
    def get_success_url(self, user):
        """Redirect based on user role"""
        if user.role == User.RoleChoices.TALENT:
            return reverse('talents:profile_edit')
        elif user.role == User.RoleChoices.CLIENT:
            return reverse('clients:profile_edit')
        return reverse('index')
```

### **Registration Forms**
```python
# backend/apps/registration/forms.py
from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.core.exceptions import ValidationError

class SignupForm(UserCreationForm):
    """
    Base signup form with email and role
    """
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'form-input',
            'placeholder': 'Enter your email address'
        })
    )
    
    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-input',
            'placeholder': 'First name'
        })
    )
    
    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-input',
            'placeholder': 'Last name'
        })
    )
    
    role = forms.ChoiceField(
        choices=User.RoleChoices.choices,
        widget=forms.HiddenInput()
    )
    
    terms_accepted = forms.BooleanField(
        required=True,
        error_messages={
            'required': 'You must accept the terms and conditions.'
        }
    )
    
    class Meta:
        model = User
        fields = ('email', 'first_name', 'last_name', 'role', 'password1', 'password2')
    
    def clean_email(self):
        email = self.cleaned_data.get('email')
        if User.objects.filter(email=email).exists():
            raise ValidationError("A user with this email already exists.")
        return email
    
    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        user.role = self.cleaned_data['role']
        
        if commit:
            user.save()
        return user

class TalentSignupForm(SignupForm):
    """
    Signup form for talents with additional fields
    """
    professional_title = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input',
            'placeholder': 'e.g., Full Stack Developer'
        })
    )
    
    def save(self, commit=True):
        user = super().save(commit)
        if commit and hasattr(user, 'talent'):
            user.talent.professional_title = self.cleaned_data.get('professional_title', '')
            user.talent.save()
        return user

class ClientSignupForm(SignupForm):
    """
    Signup form for clients with company information
    """
    company_name = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input',
            'placeholder': 'Company name (optional)'
        })
    )
    
    website = forms.URLField(
        required=False,
        widget=forms.URLInput(attrs={
            'class': 'form-input',
            'placeholder': 'https://yourcompany.com'
        })
    )
```

## 🔐 Authentication System

### **Custom Authentication Backend**
```python
# backend/apps/registration/backends.py
from django.contrib.auth.backends import ModelBackend
from django.contrib.auth import get_user_model

User = get_user_model()

class EmailBackend(ModelBackend):
    """
    Authenticate using email instead of username
    """
    
    def authenticate(self, request, username=None, password=None, **kwargs):
        try:
            # Try to get user by email
            user = User.objects.get(email=username)
            
            # Check password
            if user.check_password(password):
                return user
        except User.DoesNotExist:
            # Run the default password hasher once to reduce the timing
            # difference between an existing and a nonexistent user
            User().set_password(password)
        
        return None
    
    def get_user(self, user_id):
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None
```

### **Login and Logout Views**
```python
class SignInView(LoginView):
    """
    Custom login view with email authentication
    """
    form_class = AuthenticationCustomForm
    template_name = "registration/login.html"
    redirect_authenticated_user = True
    
    def get_success_url(self):
        # Redirect to intended page or profile
        url = self.get_redirect_url()
        return url or self.get_profile_url()
    
    def get_profile_url(self):
        """Get profile URL based on user role"""
        user = self.request.user
        
        if user.role == User.RoleChoices.CLIENT:
            return reverse_lazy("clients:dashboard")
        elif user.role == User.RoleChoices.TALENT:
            return reverse_lazy("talents:dashboard")
        
        return reverse_lazy("index")
    
    def form_valid(self, form):
        """Security check and login"""
        user = form.get_user()
        
        # Check if account is confirmed
        if not user.is_email_confirmed:
            messages.error(
                self.request,
                "Please confirm your email address before logging in."
            )
            return redirect('signup_incomplete')
        
        # Check if account is active
        if not user.is_active:
            messages.error(
                self.request,
                "Your account has been deactivated. Please contact support."
            )
            return redirect('login')
        
        return super().form_valid(form)

class CustomLogoutView(LogoutView):
    """
    Custom logout view with message
    """
    next_page = reverse_lazy('index')
    
    def dispatch(self, request, *args, **kwargs):
        messages.success(request, "You have been logged out successfully.")
        return super().dispatch(request, *args, **kwargs)
```

## 🛡️ Permission System

### **Role-Based Decorators**
```python
# backend/apps/registration/decorators.py
from functools import wraps
from django.contrib.auth.decorators import login_required
from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect

def talent_required(view_func):
    """
    Decorator for views that require talent role
    """
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        if (request.user.role != User.RoleChoices.TALENT or
            not hasattr(request.user, 'talent')):
            raise PermissionDenied("You must be a talent to access this page.")
        return view_func(request, *args, **kwargs)
    return _wrapped_view

def client_required(view_func):
    """
    Decorator for views that require client role
    """
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        if (request.user.role != User.RoleChoices.CLIENT or
            not hasattr(request.user, 'client')):
            raise PermissionDenied("You must be a client to access this page.")
        return view_func(request, *args, **kwargs)
    return _wrapped_view

def verified_talent_required(view_func):
    """
    Decorator for views that require verified talent
    """
    @wraps(view_func)
    @talent_required
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.talent.is_verified:
            messages.warning(
                request,
                "You need to complete verification to access this feature."
            )
            return redirect('talents:verification')
        return view_func(request, *args, **kwargs)
    return _wrapped_view

def profile_complete_required(view_func):
    """
    Decorator for views that require complete profile
    """
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_profile_complete():
            messages.warning(
                request,
                "Please complete your profile to access this feature."
            )
            
            if request.user.role == User.RoleChoices.TALENT:
                return redirect('talents:profile_edit')
            elif request.user.role == User.RoleChoices.CLIENT:
                return redirect('clients:profile_edit')
        
        return view_func(request, *args, **kwargs)
    return _wrapped_view
```

### **Class-Based Permission Mixins**
```python
# backend/apps/registration/mixins.py
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.core.exceptions import PermissionDenied

class TalentRequiredMixin(LoginRequiredMixin, UserPassesTestMixin):
    """
    Mixin for views that require talent role
    """
    def test_func(self):
        return (
            self.request.user.role == User.RoleChoices.TALENT and
            hasattr(self.request.user, 'talent')
        )
    
    def handle_no_permission(self):
        if not self.request.user.is_authenticated:
            return super().handle_no_permission()
        raise PermissionDenied("You must be a talent to access this page.")

class ClientRequiredMixin(LoginRequiredMixin, UserPassesTestMixin):
    """
    Mixin for views that require client role
    """
    def test_func(self):
        return (
            self.request.user.role == User.RoleChoices.CLIENT and
            hasattr(self.request.user, 'client')
        )

class VerifiedTalentRequiredMixin(TalentRequiredMixin):
    """
    Mixin for views that require verified talent
    """
    def test_func(self):
        return (
            super().test_func() and
            self.request.user.talent.is_verified
        )

class ProfileOwnerMixin(UserPassesTestMixin):
    """
    Mixin to ensure user can only access their own profile
    """
    def test_func(self):
        obj = self.get_object()
        return obj.user == self.request.user
```

## 🔑 Password Management

### **Password Reset Views**
```python
class PasswordResetCustomView(PasswordResetView):
    """
    Custom password reset view
    """
    template_name = "registration/password_reset.html"
    email_template_name = "registration/password_reset_email.html"
    subject_template_name = "registration/password_reset_subject.txt"
    success_url = reverse_lazy("password_reset_done")
    
    def form_valid(self, form):
        # Check if email exists
        email = form.cleaned_data['email']
        if not User.objects.filter(email=email, is_active=True).exists():
            messages.error(
                self.request,
                "No active account found with this email address."
            )
            return redirect('password_reset')
        
        return super().form_valid(form)

class PasswordChangeCustomView(PasswordChangeView):
    """
    Custom password change view for logged-in users
    """
    template_name = "registration/password_change.html"
    success_url = reverse_lazy("password_change_done")
    
    def form_valid(self, form):
        messages.success(
            self.request,
            "Your password has been changed successfully."
        )
        return super().form_valid(form)
```

## 🎯 Key Takeaways

1. **Email Authentication**: Custom user model with email as username
2. **Role-Based Access**: Separate roles for talents and clients
3. **Email Verification**: Secure account activation process
4. **Permission System**: Decorators and mixins for access control
5. **Profile Integration**: Automatic profile creation based on role
6. **Security Features**: Password management and account protection

## 🔗 What's Next?

Now that you understand the backend architecture, let's move to the frontend and explore how Hotwire creates dynamic user experiences.

**Next**: [Lesson 10: Hotwire (Turbo + Stimulus) Fundamentals](../03-frontend/10-hotwire-fundamentals.md)

---

## 💡 Quick Quiz

1. What field is used as USERNAME_FIELD in the custom user model?
2. How are user roles handled in the registration process?
3. What's the purpose of email confirmation?
4. How do permission mixins work in class-based views?

*Answers: 1) email, 2) Different forms and automatic profile creation based on role, 3) Security verification and account activation, 4) test_func() method checks permissions before view execution*
