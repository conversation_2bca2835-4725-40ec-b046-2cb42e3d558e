# Lesson 25: Performance Optimization ⚡

## 🎯 Learning Objectives
- Database query optimization techniques
- Caching strategies and implementation
- Frontend performance optimization
- Monitoring and profiling tools
- Scalability considerations

## 🗄️ Database Optimization

### **Query Optimization**
```python
# Inefficient queries (N+1 problem)
def bad_job_list():
    jobs = Job.objects.all()
    for job in jobs:
        print(job.client.user.name)  # N+1 queries
        print(job.skills.count())    # N+1 queries

# Optimized queries
def optimized_job_list():
    jobs = Job.objects.select_related(
        'client__user', 'category'
    ).prefetch_related(
        'skills'
    ).all()
    
    for job in jobs:
        print(job.client.user.name)  # No additional queries
        print(job.skills.count())    # No additional queries

# Complex optimization with annotations
class JobListView(ListView):
    def get_queryset(self):
        return Job.objects.select_related(
            'client__user', 'category'
        ).prefetch_related(
            'skills'
        ).annotate(
            applications_count=Count('deal'),
            avg_talent_rating=Avg('deal__talent__reviews_rating')
        ).filter(
            status=Job.StatusChoices.PUBLISHED
        ).order_by('-created_at')
```

### **Database Indexes**
```python
# Strategic index placement
class Job(models.Model):
    # ... fields ...
    
    class Meta:
        indexes = [
            # Composite indexes for common queries
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['category', 'status']),
            models.Index(fields=['budget_min', 'budget_max']),
            
            # Partial indexes for specific conditions
            models.Index(
                fields=['created_at'],
                condition=Q(status='published'),
                name='published_jobs_created_idx'
            ),
            
            # Text search indexes
            models.Index(fields=['title']),
            models.Index(fields=['description']),
        ]

# Custom database functions
from django.db.models import Func

class FullTextSearch(Func):
    function = 'to_tsvector'
    template = "%(function)s('english', %(expressions)s)"

# Usage in views
class JobSearchView(ListView):
    def get_queryset(self):
        query = self.request.GET.get('q')
        if query:
            return Job.objects.annotate(
                search_vector=FullTextSearch('title', 'description')
            ).filter(
                search_vector__icontains=query
            )
        return Job.objects.published()
```

### **Query Analysis Tools**
```python
# Debug toolbar for development
if DEBUG:
    INSTALLED_APPS += ['debug_toolbar']
    MIDDLEWARE += ['debug_toolbar.middleware.DebugToolbarMiddleware']

# Query logging
LOGGING = {
    'version': 1,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'django.db.backends': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
    },
}

# Custom query analyzer
class QueryAnalyzer:
    def __init__(self):
        self.queries = []
    
    def __enter__(self):
        from django.db import connection
        self.initial_queries = len(connection.queries)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        from django.db import connection
        self.queries = connection.queries[self.initial_queries:]
        
        print(f"Executed {len(self.queries)} queries:")
        for query in self.queries:
            print(f"  {query['time']}s: {query['sql'][:100]}...")

# Usage
with QueryAnalyzer() as analyzer:
    jobs = list(Job.objects.select_related('client').all()[:10])
```

## 🚀 Caching Strategies

### **Redis Cache Configuration**
```python
# settings/production.py
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'SERIALIZER': 'django_redis.serializers.json.JSONSerializer',
            'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
        },
        'KEY_PREFIX': 'crelancer',
        'TIMEOUT': 300,  # 5 minutes default
    },
    'sessions': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/2',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        },
        'KEY_PREFIX': 'sessions',
        'TIMEOUT': 86400,  # 24 hours
    }
}

SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
SESSION_CACHE_ALIAS = 'sessions'
```

### **View-Level Caching**
```python
from django.views.decorators.cache import cache_page
from django.core.cache import cache
from django.utils.decorators import method_decorator

# Function-based view caching
@cache_page(60 * 15)  # 15 minutes
def job_list_view(request):
    jobs = Job.objects.published().select_related('client', 'category')
    return render(request, 'jobs/list.html', {'jobs': jobs})

# Class-based view caching
@method_decorator(cache_page(60 * 15), name='dispatch')
class TalentListView(ListView):
    model = Talent
    template_name = 'talents/list.html'

# Conditional caching
class JobDetailView(DetailView):
    model = Job
    
    def dispatch(self, request, *args, **kwargs):
        # Cache only for anonymous users
        if not request.user.is_authenticated:
            return cache_page(60 * 30)(super().dispatch)(request, *args, **kwargs)
        return super().dispatch(request, *args, **kwargs)
```

### **Template Fragment Caching**
```html
<!-- Cache expensive template fragments -->
{% load cache %}

{% cache 900 job_card job.id job.updated_at %}
<div class="job-card">
    <h3>{{ job.title }}</h3>
    <p>{{ job.description|truncatewords:30 }}</p>
    
    <!-- Expensive operations -->
    {% for skill in job.skills.all %}
        <span class="skill-tag">{{ skill.name }}</span>
    {% endfor %}
    
    <div class="stats">
        Applications: {{ job.applications_count }}
        Views: {{ job.views_count }}
    </div>
</div>
{% endcache %}

<!-- Vary cache by user -->
{% cache 600 user_dashboard request.user.id %}
<div class="dashboard">
    <!-- User-specific content -->
</div>
{% endcache %}
```

### **Model-Level Caching**
```python
class CachedModelMixin:
    """Mixin to add caching to model methods"""
    
    def get_cache_key(self, method_name, *args):
        return f"{self._meta.label_lower}:{self.pk}:{method_name}:{hash(args)}"
    
    def cached_method(self, method_name, timeout=300):
        def decorator(func):
            def wrapper(*args, **kwargs):
                cache_key = self.get_cache_key(method_name, *args)
                result = cache.get(cache_key)
                
                if result is None:
                    result = func(*args, **kwargs)
                    cache.set(cache_key, result, timeout)
                
                return result
            return wrapper
        return decorator

class Talent(CachedModelMixin, models.Model):
    # ... fields ...
    
    @cached_method('get_average_rating', timeout=3600)
    def get_average_rating(self):
        return self.received_reviews.aggregate(
            avg_rating=Avg('rating')
        )['avg_rating'] or 0
    
    @cached_method('get_completed_jobs_count', timeout=1800)
    def get_completed_jobs_count(self):
        return self.deal_set.filter(
            status=Deal.StatusChoices.COMPLETED
        ).count()
    
    def save(self, *args, **kwargs):
        # Invalidate cache on save
        cache_keys = [
            self.get_cache_key('get_average_rating'),
            self.get_cache_key('get_completed_jobs_count'),
        ]
        cache.delete_many(cache_keys)
        
        super().save(*args, **kwargs)
```

## 🎨 Frontend Optimization

### **Asset Optimization**
```javascript
// webpack.config.prod.js - Advanced optimization
module.exports = {
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true,
            pure_funcs: ['console.log', 'console.info']
          }
        }
      }),
      new CssMinimizerPlugin()
    ],
    
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
        common: {
          name: 'common',
          minChunks: 2,
          chunks: 'all',
          enforce: true
        }
      }
    }
  },
  
  plugins: [
    // Preload critical resources
    new PreloadWebpackPlugin({
      rel: 'preload',
      as: 'font',
      include: 'allAssets',
      fileWhitelist: [/\.woff2$/]
    }),
    
    // Generate service worker for caching
    new WorkboxPlugin.GenerateSW({
      clientsClaim: true,
      skipWaiting: true,
      runtimeCaching: [{
        urlPattern: /^https:\/\/fonts\.googleapis\.com/,
        handler: 'StaleWhileRevalidate',
        options: {
          cacheName: 'google-fonts-stylesheets',
        }
      }]
    })
  ]
}
```

### **Image Optimization**
```python
# Automatic image optimization
from PIL import Image
import io
from django.core.files.base import ContentFile

class OptimizedImageField(models.ImageField):
    def save_form_data(self, instance, data):
        if data and hasattr(data, 'read'):
            # Optimize image before saving
            optimized = self.optimize_image(data)
            super().save_form_data(instance, optimized)
        else:
            super().save_form_data(instance, data)
    
    def optimize_image(self, image_file, quality=85, max_width=1920):
        try:
            image = Image.open(image_file)
            
            # Convert to RGB if necessary
            if image.mode in ('RGBA', 'LA', 'P'):
                image = image.convert('RGB')
            
            # Resize if too large
            if image.width > max_width:
                ratio = max_width / image.width
                new_height = int(image.height * ratio)
                image = image.resize((max_width, new_height), Image.Resampling.LANCZOS)
            
            # Save optimized
            output = io.BytesIO()
            image.save(output, format='JPEG', quality=quality, optimize=True)
            output.seek(0)
            
            return ContentFile(output.read(), name=image_file.name)
        except Exception:
            return image_file

# WebP format support
class WebPImageField(OptimizedImageField):
    def optimize_image(self, image_file, quality=85):
        try:
            image = Image.open(image_file)
            
            if image.mode in ('RGBA', 'LA', 'P'):
                image = image.convert('RGB')
            
            output = io.BytesIO()
            image.save(output, format='WebP', quality=quality, optimize=True)
            output.seek(0)
            
            # Change extension to .webp
            name = image_file.name.rsplit('.', 1)[0] + '.webp'
            return ContentFile(output.read(), name=name)
        except Exception:
            return super().optimize_image(image_file, quality)
```

### **Lazy Loading Implementation**
```javascript
// Intersection Observer for lazy loading
class LazyLoader {
  constructor() {
    this.imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target
          img.src = img.dataset.src
          img.classList.remove('lazy')
          observer.unobserve(img)
        }
      })
    })
    
    this.init()
  }
  
  init() {
    const lazyImages = document.querySelectorAll('img[data-src]')
    lazyImages.forEach(img => this.imageObserver.observe(img))
  }
}

// Stimulus controller for lazy loading
export default class extends Controller {
  static targets = ["image"]
  
  connect() {
    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.loadImage(entry.target)
        }
      })
    })
    
    this.imageTargets.forEach(img => this.observer.observe(img))
  }
  
  disconnect() {
    if (this.observer) {
      this.observer.disconnect()
    }
  }
  
  loadImage(img) {
    img.src = img.dataset.src
    img.classList.add('loaded')
    this.observer.unobserve(img)
  }
}
```

## 📊 Performance Monitoring

### **Application Performance Monitoring**
```python
# Custom middleware for performance tracking
import time
from django.utils.deprecation import MiddlewareMixin

class PerformanceMiddleware(MiddlewareMixin):
    def process_request(self, request):
        request.start_time = time.time()
    
    def process_response(self, request, response):
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time
            
            # Log slow requests
            if duration > 1.0:  # Slower than 1 second
                logger.warning(
                    f"Slow request: {request.method} {request.path} "
                    f"took {duration:.2f}s"
                )
            
            # Add performance header
            response['X-Response-Time'] = f"{duration:.3f}s"
            
            # Send metrics to monitoring service
            if hasattr(settings, 'METRICS_BACKEND'):
                send_performance_metric.delay(
                    path=request.path,
                    method=request.method,
                    duration=duration,
                    status_code=response.status_code
                )
        
        return response

# Database query monitoring
from django.db import connection

class QueryCountMiddleware(MiddlewareMixin):
    def process_response(self, request, response):
        query_count = len(connection.queries)
        
        if query_count > 50:  # Too many queries
            logger.warning(
                f"High query count: {query_count} queries for "
                f"{request.method} {request.path}"
            )
        
        response['X-Query-Count'] = str(query_count)
        return response
```

### **Custom Performance Metrics**
```python
# Performance tracking service
class PerformanceTracker:
    @staticmethod
    def track_view_performance(view_name, duration, query_count):
        """Track view performance metrics"""
        cache_key = f"perf:view:{view_name}:daily"
        
        # Get existing data
        data = cache.get(cache_key, {
            'total_requests': 0,
            'total_duration': 0,
            'total_queries': 0,
            'max_duration': 0,
            'slow_requests': 0
        })
        
        # Update metrics
        data['total_requests'] += 1
        data['total_duration'] += duration
        data['total_queries'] += query_count
        data['max_duration'] = max(data['max_duration'], duration)
        
        if duration > 1.0:
            data['slow_requests'] += 1
        
        # Cache for 24 hours
        cache.set(cache_key, data, 86400)
    
    @staticmethod
    def get_performance_summary():
        """Get performance summary for dashboard"""
        views = ['job_list', 'talent_list', 'job_detail', 'talent_detail']
        summary = {}
        
        for view in views:
            cache_key = f"perf:view:{view}:daily"
            data = cache.get(cache_key, {})
            
            if data.get('total_requests', 0) > 0:
                summary[view] = {
                    'avg_duration': data['total_duration'] / data['total_requests'],
                    'avg_queries': data['total_queries'] / data['total_requests'],
                    'max_duration': data['max_duration'],
                    'slow_request_rate': data['slow_requests'] / data['total_requests'],
                    'total_requests': data['total_requests']
                }
        
        return summary

# Performance dashboard view
class PerformanceDashboardView(UserPassesTestMixin, TemplateView):
    template_name = "admin/performance_dashboard.html"
    
    def test_func(self):
        return self.request.user.is_staff
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['performance_summary'] = PerformanceTracker.get_performance_summary()
        
        # Cache hit rates
        context['cache_stats'] = self.get_cache_stats()
        
        return context
    
    def get_cache_stats(self):
        """Get cache hit/miss statistics"""
        from django_redis import get_redis_connection
        
        redis_conn = get_redis_connection("default")
        info = redis_conn.info()
        
        return {
            'hits': info.get('keyspace_hits', 0),
            'misses': info.get('keyspace_misses', 0),
            'hit_rate': info.get('keyspace_hits', 0) / 
                       max(info.get('keyspace_hits', 0) + info.get('keyspace_misses', 0), 1)
        }
```

## 🔧 Scalability Considerations

### **Database Scaling**
```python
# Read/Write database splitting
class DatabaseRouter:
    """Route reads to read replica, writes to primary"""
    
    def db_for_read(self, model, **hints):
        if model._meta.app_label in ['jobs', 'talents', 'reviews']:
            return 'read_replica'
        return 'default'
    
    def db_for_write(self, model, **hints):
        return 'default'
    
    def allow_migrate(self, db, app_label, model_name=None, **hints):
        return db == 'default'

# Database configuration
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'crelancer_primary',
        'HOST': 'primary-db.example.com',
        # ... other settings
    },
    'read_replica': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'crelancer_replica',
        'HOST': 'replica-db.example.com',
        # ... other settings
    }
}

DATABASE_ROUTERS = ['backend.core.routers.DatabaseRouter']
```

### **Celery Task Optimization**
```python
# Optimized Celery configuration
CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'

# Task routing
CELERY_TASK_ROUTES = {
    'notifications.*': {'queue': 'notifications'},
    'payments.*': {'queue': 'payments'},
    'emails.*': {'queue': 'emails'},
    '*': {'queue': 'default'},
}

# Worker optimization
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_TASK_ACKS_LATE = True
CELERY_WORKER_MAX_TASKS_PER_CHILD = 1000

# Task optimization
@shared_task(bind=True, max_retries=3)
def send_bulk_notifications(self, notification_ids):
    """Optimized bulk notification sending"""
    try:
        notifications = Notification.objects.filter(
            id__in=notification_ids
        ).select_related('recipient')
        
        # Batch process notifications
        for notification in notifications:
            try:
                EmailService.send_notification_email(notification)
            except Exception as e:
                logger.error(f"Failed to send notification {notification.id}: {e}")
                
    except Exception as exc:
        # Retry with exponential backoff
        raise self.retry(exc=exc, countdown=60 * (2 ** self.request.retries))
```

## 🎯 Key Takeaways

1. **Database Optimization**: Strategic indexing and query optimization
2. **Caching Strategy**: Multi-level caching for different use cases
3. **Frontend Performance**: Asset optimization and lazy loading
4. **Monitoring**: Track performance metrics and identify bottlenecks
5. **Scalability**: Plan for growth with proper architecture

## 🔗 What's Next?

Now that you understand advanced topics, let's move to deployment and see how to get Crelancer running in production.

**Next**: [Lesson 26: Docker & Containerization](../06-deployment/26-docker.md)
