from django.shortcuts import redirect
from django.urls import reverse
from django.contrib.auth.mixins import LoginRequiredMixin


class ClientProfileRequiredMixin(LoginRequiredMixin):
    """
    Checks if the user has a client profile.
    If not - redirects to the client creation page.

    Also inherits from LoginRequiredMixin to check if the user is authenticated.
    If not - redirects to the login page.
    """

    def dispatch(self, request, *args, **kwargs):
        if request.user.is_authenticated and not hasattr(request.user, "client"):
            return redirect(reverse("client_create"))
        return super().dispatch(request, *args, **kwargs)
