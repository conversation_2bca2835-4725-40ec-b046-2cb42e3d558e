# Lesson 23: Notifications & Email System 🔔

## 🎯 Learning Objectives
- Multi-channel notification system
- Email templates and delivery
- Push notifications and real-time alerts
- Notification preferences and management
- Performance optimization and queuing

## 🔔 Notification Architecture

### **Notification Model**
```python
class Notification(models.Model):
    class NotificationType(models.TextChoices):
        MESSAGE = 'message', 'New Message'
        JOB_APPLICATION = 'job_application', 'Job Application'
        HIRE = 'hire', 'Hired for Job'
        MILESTONE_SUBMITTED = 'milestone_submitted', 'Milestone Submitted'
        MILESTONE_APPROVED = 'milestone_approved', 'Milestone Approved'
        PAYMENT_RECEIVED = 'payment_received', 'Payment Received'
        REVIEW_RECEIVED = 'review_received', 'Review Received'
        VERIFICATION_COMPLETE = 'verification_complete', 'Verification Complete'
    
    class Channel(models.TextChoices):
        EMAIL = 'email', 'Email'
        PUSH = 'push', 'Push Notification'
        IN_APP = 'in_app', 'In-App Notification'
        SMS = 'sms', 'SMS'
    
    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    notification_type = models.CharField(max_length=30, choices=NotificationType.choices)
    channel = models.CharField(max_length=10, choices=Channel.choices)
    
    # Content
    title = models.CharField(max_length=255)
    message = models.TextField()
    action_url = models.URLField(blank=True)
    
    # Metadata
    data = models.JSONField(default=dict)
    
    # Status
    is_sent = models.BooleanField(default=False)
    is_read = models.BooleanField(default=False)
    sent_at = models.DateTimeField(null=True)
    read_at = models.DateTimeField(null=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['recipient', 'is_read']),
            models.Index(fields=['notification_type', 'created_at']),
        ]
```

### **Notification Preferences**
```python
class NotificationPreference(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    
    # Email preferences
    email_messages = models.BooleanField(default=True)
    email_job_updates = models.BooleanField(default=True)
    email_payment_updates = models.BooleanField(default=True)
    email_marketing = models.BooleanField(default=False)
    
    # Push notification preferences
    push_messages = models.BooleanField(default=True)
    push_job_updates = models.BooleanField(default=True)
    push_payment_updates = models.BooleanField(default=True)
    
    # Frequency settings
    email_frequency = models.CharField(
        max_length=20,
        choices=[
            ('immediate', 'Immediate'),
            ('daily', 'Daily Digest'),
            ('weekly', 'Weekly Digest'),
            ('never', 'Never')
        ],
        default='immediate'
    )
    
    # Quiet hours
    quiet_hours_start = models.TimeField(default='22:00')
    quiet_hours_end = models.TimeField(default='08:00')
    timezone = models.CharField(max_length=50, default='UTC')
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

## 📧 Email System

### **Email Service**
```python
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from celery import shared_task

class EmailService:
    @staticmethod
    def send_notification_email(notification):
        """Send notification via email"""
        user = notification.recipient
        
        # Check if user wants email notifications
        prefs = getattr(user, 'notificationpreference', None)
        if prefs and not EmailService.should_send_email(notification, prefs):
            return False
        
        # Get email template
        template_name = f"emails/{notification.notification_type}.html"
        text_template = f"emails/{notification.notification_type}.txt"
        
        context = {
            'user': user,
            'notification': notification,
            'site_url': settings.SITE_URL,
            **notification.data
        }
        
        try:
            # Render email content
            html_content = render_to_string(template_name, context)
            text_content = render_to_string(text_template, context)
            
            # Create email
            email = EmailMultiAlternatives(
                subject=notification.title,
                body=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[user.email]
            )
            email.attach_alternative(html_content, "text/html")
            
            # Send email
            email.send()
            
            # Update notification status
            notification.is_sent = True
            notification.sent_at = timezone.now()
            notification.save()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email notification {notification.id}: {e}")
            return False
    
    @staticmethod
    def should_send_email(notification, preferences):
        """Check if email should be sent based on preferences"""
        # Check notification type preferences
        type_mapping = {
            'message': preferences.email_messages,
            'job_application': preferences.email_job_updates,
            'hire': preferences.email_job_updates,
            'payment_received': preferences.email_payment_updates,
        }
        
        if not type_mapping.get(notification.notification_type, True):
            return False
        
        # Check frequency settings
        if preferences.email_frequency == 'never':
            return False
        
        # Check quiet hours
        if EmailService.is_quiet_hours(preferences):
            return False
        
        return True
    
    @staticmethod
    def is_quiet_hours(preferences):
        """Check if current time is within user's quiet hours"""
        from pytz import timezone as pytz_timezone
        
        user_tz = pytz_timezone(preferences.timezone)
        current_time = timezone.now().astimezone(user_tz).time()
        
        start = preferences.quiet_hours_start
        end = preferences.quiet_hours_end
        
        if start <= end:
            return start <= current_time <= end
        else:  # Quiet hours span midnight
            return current_time >= start or current_time <= end

@shared_task
def send_email_notification(notification_id):
    """Celery task to send email notification"""
    try:
        notification = Notification.objects.get(id=notification_id)
        EmailService.send_notification_email(notification)
    except Notification.DoesNotExist:
        logger.error(f"Notification {notification_id} not found")
```

### **Email Templates**
```html
<!-- emails/base.html -->
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Crelancer{% endblock %}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #0ea5e9; color: white; padding: 20px; text-align: center; }
        .content { padding: 30px 20px; background: #f9f9f9; }
        .button { display: inline-block; padding: 12px 24px; background: #0ea5e9; color: white; text-decoration: none; border-radius: 5px; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Crelancer</h1>
        </div>
        
        <div class="content">
            {% block content %}{% endblock %}
        </div>
        
        <div class="footer">
            <p>
                You received this email because you have an account on Crelancer.
                <a href="{{ site_url }}/settings/notifications/">Manage your notification preferences</a>
            </p>
            <p>&copy; 2024 Crelancer. All rights reserved.</p>
        </div>
    </div>
</body>
</html>

<!-- emails/message.html -->
{% extends 'emails/base.html' %}

{% block title %}New Message - Crelancer{% endblock %}

{% block content %}
<h2>You have a new message</h2>

<p>Hi {{ user.first_name }},</p>

<p>You received a new message from <strong>{{ sender_name }}</strong> in your project "{{ project_title }}":</p>

<blockquote style="border-left: 4px solid #0ea5e9; padding-left: 15px; margin: 20px 0; font-style: italic;">
    {{ message_preview }}
</blockquote>

<p>
    <a href="{{ action_url }}" class="button">View Message</a>
</p>

<p>Best regards,<br>The Crelancer Team</p>
{% endblock %}
```

## 📱 Push Notifications

### **Push Notification Service**
```python
from pyfcm import FCMNotification

class PushNotificationService:
    def __init__(self):
        self.fcm = FCMNotification(api_key=settings.FCM_SERVER_KEY)
    
    def send_push_notification(self, notification):
        """Send push notification to user's devices"""
        user = notification.recipient
        
        # Get user's device tokens
        device_tokens = UserDevice.objects.filter(
            user=user,
            is_active=True
        ).values_list('fcm_token', flat=True)
        
        if not device_tokens:
            return False
        
        try:
            # Prepare notification data
            data = {
                'notification_id': str(notification.id),
                'type': notification.notification_type,
                'action_url': notification.action_url,
                **notification.data
            }
            
            # Send to multiple devices
            result = self.fcm.notify_multiple_devices(
                registration_ids=list(device_tokens),
                message_title=notification.title,
                message_body=notification.message,
                data_message=data,
                sound='default',
                badge=self.get_unread_count(user)
            )
            
            # Update notification status
            notification.is_sent = True
            notification.sent_at = timezone.now()
            notification.save()
            
            # Clean up invalid tokens
            if result.get('failure'):
                self.cleanup_invalid_tokens(result, device_tokens)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send push notification {notification.id}: {e}")
            return False
    
    def get_unread_count(self, user):
        """Get unread notification count for badge"""
        return Notification.objects.filter(
            recipient=user,
            channel=Notification.Channel.IN_APP,
            is_read=False
        ).count()
    
    def cleanup_invalid_tokens(self, result, device_tokens):
        """Remove invalid FCM tokens"""
        if 'results' in result:
            for i, res in enumerate(result['results']):
                if res.get('error') in ['InvalidRegistration', 'NotRegistered']:
                    token = device_tokens[i]
                    UserDevice.objects.filter(fcm_token=token).delete()

class UserDevice(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    fcm_token = models.CharField(max_length=255, unique=True)
    device_type = models.CharField(max_length=20)  # 'ios', 'android', 'web'
    device_name = models.CharField(max_length=255, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    last_used = models.DateTimeField(auto_now=True)
```

## 🔄 Notification Manager

### **Unified Notification Service**
```python
class NotificationManager:
    @staticmethod
    def send_notification(user, notification_type, title, message, **kwargs):
        """Send notification through all enabled channels"""
        # Get user preferences
        prefs = getattr(user, 'notificationpreference', None)
        
        # Determine channels to use
        channels = NotificationManager.get_enabled_channels(notification_type, prefs)
        
        notifications_sent = []
        
        for channel in channels:
            notification = Notification.objects.create(
                recipient=user,
                notification_type=notification_type,
                channel=channel,
                title=title,
                message=message,
                action_url=kwargs.get('action_url', ''),
                data=kwargs.get('data', {})
            )
            
            # Send through appropriate channel
            if channel == Notification.Channel.EMAIL:
                send_email_notification.delay(notification.id)
            elif channel == Notification.Channel.PUSH:
                send_push_notification.delay(notification.id)
            elif channel == Notification.Channel.IN_APP:
                # In-app notifications are stored and displayed in UI
                notification.is_sent = True
                notification.sent_at = timezone.now()
                notification.save()
            
            notifications_sent.append(notification)
        
        return notifications_sent
    
    @staticmethod
    def get_enabled_channels(notification_type, preferences):
        """Get enabled channels for notification type"""
        channels = [Notification.Channel.IN_APP]  # Always include in-app
        
        if not preferences:
            return channels
        
        # Check email preferences
        email_enabled = {
            'message': preferences.email_messages,
            'job_application': preferences.email_job_updates,
            'hire': preferences.email_job_updates,
            'payment_received': preferences.email_payment_updates,
        }.get(notification_type, True)
        
        if email_enabled and preferences.email_frequency != 'never':
            channels.append(Notification.Channel.EMAIL)
        
        # Check push preferences
        push_enabled = {
            'message': preferences.push_messages,
            'job_application': preferences.push_job_updates,
            'hire': preferences.push_job_updates,
            'payment_received': preferences.push_payment_updates,
        }.get(notification_type, True)
        
        if push_enabled:
            channels.append(Notification.Channel.PUSH)
        
        return channels

# Convenience functions for common notifications
def notify_new_message(deal, sender, message_content):
    """Notify about new chat message"""
    recipient = deal.talent.user if sender == deal.client.user else deal.client.user
    
    NotificationManager.send_notification(
        user=recipient,
        notification_type='message',
        title=f"New message from {sender.get_full_name()}",
        message=message_content[:100] + "..." if len(message_content) > 100 else message_content,
        action_url=f"/chat/{deal.id}/",
        data={
            'deal_id': deal.id,
            'sender_name': sender.get_full_name(),
            'project_title': deal.job.title
        }
    )

def notify_job_application(job, talent):
    """Notify client about new job application"""
    NotificationManager.send_notification(
        user=job.client.user,
        notification_type='job_application',
        title=f"New application for {job.title}",
        message=f"{talent.user.get_full_name()} applied for your job posting.",
        action_url=f"/jobs/{job.id}/applications/",
        data={
            'job_id': job.id,
            'talent_name': talent.user.get_full_name(),
            'talent_id': talent.id
        }
    )
```

## 📊 Notification Analytics

### **Analytics Dashboard**
```python
class NotificationAnalyticsView(UserPassesTestMixin, TemplateView):
    template_name = "admin/notification_analytics.html"
    
    def test_func(self):
        return self.request.user.is_staff
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Delivery rates by channel
        delivery_rates = Notification.objects.values('channel').annotate(
            total=Count('id'),
            sent=Count('id', filter=Q(is_sent=True)),
            read=Count('id', filter=Q(is_read=True))
        )
        
        # Popular notification types
        notification_types = Notification.objects.values('notification_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        # User engagement
        user_engagement = User.objects.annotate(
            notifications_received=Count('notifications'),
            notifications_read=Count('notifications', filter=Q(notifications__is_read=True))
        ).filter(notifications_received__gt=0)
        
        context.update({
            'delivery_rates': delivery_rates,
            'notification_types': notification_types,
            'avg_read_rate': user_engagement.aggregate(
                avg_rate=Avg(F('notifications_read') * 100.0 / F('notifications_received'))
            )['avg_rate'] or 0
        })
        
        return context
```

## 🎯 Key Takeaways

1. **Multi-Channel System**: Email, push, and in-app notifications
2. **User Preferences**: Granular control over notification settings
3. **Template System**: Consistent, branded email templates
4. **Performance**: Asynchronous delivery with Celery
5. **Analytics**: Track delivery and engagement rates

## 🔗 What's Next?

**Next**: [Lesson 24: Testing Strategies](./24-testing.md)
