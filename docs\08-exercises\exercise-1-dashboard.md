# Exercise 1: Custom User Dashboard 📊

## 🎯 Objective

Create personalized dashboards for both talents and clients that display role-specific information, quick actions, and relevant metrics. This exercise will help you practice Django views, templates, user authentication, and database queries.

## 📋 Prerequisites

- Completed Lessons 1-9 (Project Setup and Backend Development)
- Understanding of Django views and templates
- Basic knowledge of user authentication and permissions

## 🎨 What You'll Build

### **Talent Dashboard**
- Profile completion status
- Active job applications
- Recent messages
- Earnings summary
- Quick actions (update profile, browse jobs)

### **Client Dashboard**
- Posted jobs overview
- Application statistics
- Active projects
- Recent activity feed
- Quick actions (post job, manage applications)

## 🛠️ Implementation Steps

### **Step 1: Create Dashboard Views**

```python
# backend/apps/dashboard/views.py
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Count, Sum, Q
from django.utils import timezone
from datetime import timedelta

from jobs.models import Job, Deal
from chat.models import Message
from finance.models import Transaction

@login_required
def dashboard_view(request):
    """Route to appropriate dashboard based on user role"""
    if request.user.role == 'talent':
        return talent_dashboard(request)
    elif request.user.role == 'client':
        return client_dashboard(request)
    else:
        return render(request, 'dashboard/admin_dashboard.html')

def talent_dashboard(request):
    """Dashboard for talent users"""
    talent = request.user.talent
    
    # Profile completion
    profile_completion = calculate_profile_completion(talent)
    
    # Active applications
    active_applications = Deal.objects.filter(
        talent=talent,
        status__in=['pending', 'active']
    ).select_related('job', 'client__user')[:5]
    
    # Recent messages
    recent_messages = Message.objects.filter(
        deal__talent=talent
    ).exclude(
        sender=request.user
    ).order_by('-created_at')[:5]
    
    # Earnings this month
    current_month = timezone.now().replace(day=1)
    monthly_earnings = Transaction.objects.filter(
        deal__talent=talent,
        transaction_type='payout',
        status='completed',
        created_at__gte=current_month
    ).aggregate(total=Sum('amount'))['total'] or 0
    
    # Statistics
    stats = {
        'total_applications': Deal.objects.filter(talent=talent).count(),
        'active_projects': Deal.objects.filter(talent=talent, status='active').count(),
        'completed_projects': Deal.objects.filter(talent=talent, status='completed').count(),
        'total_earnings': Transaction.objects.filter(
            deal__talent=talent,
            transaction_type='payout',
            status='completed'
        ).aggregate(total=Sum('amount'))['total'] or 0
    }
    
    context = {
        'profile_completion': profile_completion,
        'active_applications': active_applications,
        'recent_messages': recent_messages,
        'monthly_earnings': monthly_earnings,
        'stats': stats,
    }
    
    return render(request, 'dashboard/talent_dashboard.html', context)

def client_dashboard(request):
    """Dashboard for client users"""
    client = request.user.client
    
    # Posted jobs
    posted_jobs = Job.objects.filter(
        client=client
    ).annotate(
        applications_count=Count('deal')
    ).order_by('-created_at')[:5]
    
    # Active projects
    active_projects = Deal.objects.filter(
        client=client,
        status='active'
    ).select_related('talent__user', 'job')[:5]
    
    # Recent activity
    recent_activity = get_recent_activity(client)
    
    # Spending this month
    current_month = timezone.now().replace(day=1)
    monthly_spending = Transaction.objects.filter(
        deal__client=client,
        transaction_type='payment',
        status='completed',
        created_at__gte=current_month
    ).aggregate(total=Sum('amount'))['total'] or 0
    
    # Statistics
    stats = {
        'total_jobs_posted': Job.objects.filter(client=client).count(),
        'active_projects': Deal.objects.filter(client=client, status='active').count(),
        'completed_projects': Deal.objects.filter(client=client, status='completed').count(),
        'total_spent': Transaction.objects.filter(
            deal__client=client,
            transaction_type='payment',
            status='completed'
        ).aggregate(total=Sum('amount'))['total'] or 0
    }
    
    context = {
        'posted_jobs': posted_jobs,
        'active_projects': active_projects,
        'recent_activity': recent_activity,
        'monthly_spending': monthly_spending,
        'stats': stats,
    }
    
    return render(request, 'dashboard/client_dashboard.html', context)

def calculate_profile_completion(talent):
    """Calculate profile completion percentage"""
    total_fields = 10
    completed_fields = 0
    
    # Check required fields
    if talent.professional_title:
        completed_fields += 1
    if talent.about:
        completed_fields += 1
    if talent.rate_hourly:
        completed_fields += 1
    if talent.skills.exists():
        completed_fields += 1
    if talent.user.first_name and talent.user.last_name:
        completed_fields += 1
    if hasattr(talent.user, 'profile_image') and talent.user.profile_image:
        completed_fields += 1
    if talent.experience_years:
        completed_fields += 1
    if talent.projects.exists():
        completed_fields += 1
    if talent.is_verified:
        completed_fields += 1
    if talent.location:
        completed_fields += 1
    
    return (completed_fields / total_fields) * 100

def get_recent_activity(client):
    """Get recent activity for client"""
    activities = []
    
    # Recent applications
    recent_applications = Deal.objects.filter(
        client=client,
        created_at__gte=timezone.now() - timedelta(days=7)
    ).select_related('talent__user', 'job')
    
    for application in recent_applications:
        activities.append({
            'type': 'application',
            'message': f"{application.talent.user.get_full_name()} applied for {application.job.title}",
            'timestamp': application.created_at,
            'url': f"/jobs/{application.job.id}/applications/"
        })
    
    # Recent messages
    recent_messages = Message.objects.filter(
        deal__client=client,
        created_at__gte=timezone.now() - timedelta(days=7)
    ).exclude(sender=client.user).select_related('sender', 'deal')
    
    for message in recent_messages:
        activities.append({
            'type': 'message',
            'message': f"New message from {message.sender.get_full_name()}",
            'timestamp': message.created_at,
            'url': f"/chat/{message.deal.id}/"
        })
    
    # Sort by timestamp
    activities.sort(key=lambda x: x['timestamp'], reverse=True)
    
    return activities[:10]
```

### **Step 2: Create Dashboard Templates**

```html
<!-- backend/templates/dashboard/talent_dashboard.html -->
{% extends 'base.html' %}
{% load humanize %}

{% block title %}Dashboard - Crelancer{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Welcome back, {{ user.first_name }}!</h1>
        <p class="text-gray-600 mt-2">Here's what's happening with your freelance work.</p>
    </div>

    <!-- Profile Completion Alert -->
    {% if profile_completion < 100 %}
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                </svg>
            </div>
            <div class="ml-3 flex-1">
                <h3 class="text-sm font-medium text-yellow-800">Complete your profile</h3>
                <p class="text-sm text-yellow-700 mt-1">
                    Your profile is {{ profile_completion|floatformat:0 }}% complete. 
                    <a href="{% url 'talents:profile_edit' %}" class="font-medium underline">Complete it now</a> to get more job opportunities.
                </p>
            </div>
            <div class="ml-3">
                <div class="w-16 h-2 bg-yellow-200 rounded-full">
                    <div class="h-2 bg-yellow-500 rounded-full" style="width: {{ profile_completion }}%"></div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Applications</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ stats.total_applications }}</dd>
                    </dl>
                </div>
            </div>
        </div>
        <!-- Additional stat cards... -->
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Active Applications -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Active Applications</h3>
                </div>
                <div class="p-6">
                    {% if active_applications %}
                        <div class="space-y-4">
                            {% for application in active_applications %}
                            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                <div class="flex-1">
                                    <h4 class="text-sm font-medium text-gray-900">
                                        <a href="{% url 'jobs:detail' application.job.id %}" class="hover:text-blue-600">
                                            {{ application.job.title }}
                                        </a>
                                    </h4>
                                    <p class="text-sm text-gray-500 mt-1">
                                        Applied {{ application.created_at|timesince }} ago
                                    </p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if application.status == 'pending' %}bg-yellow-100 text-yellow-800
                                        {% elif application.status == 'active' %}bg-green-100 text-green-800
                                        {% endif %}">
                                        {{ application.get_status_display }}
                                    </span>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-8">
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No active applications</h3>
                            <p class="mt-1 text-sm text-gray-500">Start applying for jobs to see them here.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <a href="{% url 'jobs:list' %}" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md border border-gray-200">
                        Browse Jobs
                    </a>
                    <a href="{% url 'talents:profile_edit' %}" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md border border-gray-200">
                        Update Profile
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
```

## ✅ Expected Outcome

After completing this exercise, you should have:

1. **Functional Dashboards**: Separate dashboards for talents and clients
2. **Role-based Content**: Different information displayed based on user role
3. **Real Data**: Dashboard populated with actual user data
4. **Responsive Design**: Works well on desktop and mobile
5. **Quick Actions**: Easy access to common tasks

## 🚀 Extension Challenges

1. **Add Charts**: Use Chart.js to visualize earnings/spending trends
2. **Real-time Updates**: Use WebSockets to update dashboard data live
3. **Customizable Widgets**: Allow users to customize their dashboard layout
4. **Mobile App**: Create a mobile-friendly version with different layout
5. **Admin Dashboard**: Create a comprehensive admin dashboard with platform metrics

## 💡 Solution Tips

- Use Django's `select_related()` and `prefetch_related()` for efficient queries
- Implement proper error handling for missing data
- Add loading states for better user experience
- Use template fragments for reusable components
- Consider caching for frequently accessed data

## 🔍 Testing Your Implementation

1. **Create Test Users**: Both talent and client accounts
2. **Add Sample Data**: Jobs, applications, messages, transactions
3. **Test Role Switching**: Verify different content for different roles
4. **Check Responsiveness**: Test on different screen sizes
5. **Verify Links**: Ensure all quick actions work correctly

Great job! You've created personalized dashboards that provide users with a comprehensive overview of their activity on the platform. This exercise reinforced your understanding of Django views, templates, and database queries while creating a practical, user-focused feature.

**Next**: [Exercise 2: Advanced Job Search](./exercise-2-search.md)
