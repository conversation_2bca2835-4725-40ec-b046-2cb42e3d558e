import csv
from django.http import HttpResponse
from django.contrib import admin, messages
from django.utils.safestring import mark_safe
from .models import Client


@admin.register(Client)
class ClientProfileAdmin(admin.ModelAdmin):
    """
    Admin panel for ClientProfile model.
    Combines functionality of Profile and User admin panels.
    """

    list_display = (
        "id",
        "user_fullname",
        "user_company",
        "user_account",
    )
    readonly_fields = (
        "user_fullname",
        "user_account",
        "user_company",
        "stripe_customer",
        "stripe_card",
    )
    list_display_links = ("id", "user_fullname")
    fieldsets = (
        (
            "User information",
            {"fields": ("user_fullname", "user_account")},
        ),
        (
            "Basic profile information",
            {"fields": ("user_company", "about", "website")},
        ),
        (
            "Payment information",
            {"fields": ("stripe_customer", "stripe_card")},
        ),
    )
    actions = ["export_as_csv"]

    @admin.display(description="User name")
    def user_fullname(self, obj):
        return obj.user.get_full_name()

    @admin.display(description="Company name")
    def user_company(self, obj):
        return obj.user.company

    @admin.display(description="User account")
    @mark_safe
    def user_account(self, obj):
        return (
            f'<a href="/admin/registration/user/{obj.user.id}/change/">'
            f"{obj.user.email}</a>"
        )

    @admin.display(description="Stripe customer")
    @mark_safe
    def stripe_customer(self, obj):
        return "<a href='https://dashboard.stripe.com/customers/{}' target='_blank'>{}</a>".format(
            obj.stripe_customer_id, obj.stripe_customer_id
        )

    @admin.display(description="Stripe linked card")
    @mark_safe
    def stripe_card(self, obj):
        if obj.stripe_card_data:
            return f"{obj.stripe_card_data['brand'].upper()} **** **** **** {obj.stripe_card_data['last4']}"
        return "No card linked..."

    @admin.action(description="Export selected clients as CSV")
    def export_as_csv(self, request, queryset):
        response = HttpResponse(content_type="text/csv")
        response["Content-Disposition"] = "attachment; filename=clients_export.csv"
        writer = csv.writer(response)

        writer.writerow(
            [
                "ID",
                "Email",
                "First name",
                "Last name",
                "State",
                "City",
                "About",
                "Website",
            ]
        )
        for obj in queryset:
            data = [
                obj.id,
                obj.user.email,
                obj.user.first_name,
                obj.user.last_name,
                obj.user.state,
                obj.user.city,
                obj.about,
                obj.website,
            ]
            row = writer.writerow(data)

        return response

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False
