{% extends "chat/main.html" %}
{% load turbo_streams %}
{% load static %}

{% block messages %}
    <turbo-frame id="chat-messages">
        {% turbo_subscribe deal %}

        <div data-controller="scroll-to-down"
             class="justify-between flex flex-col h-[calc(100vh-100px)] md:h-[calc(100vh-124px)]">
            <div class="p-4 border-b border-gray-300">
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center">

                    <div class="flex flex-nowrap justify-start gap-x-3 items-center">
                    <!--if user = client-->
                    {% if user == deal.client.user %}
                    <a href="{% url "talents:detail" deal.talent.pk %}" data-turbo="false" target="_blank">
                        <button type="button" data-action="click->chat#hideMessages" class="md:hidden">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" class="w-6 h-6 text-base-content/60">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18"></path>
                            </svg>
                        </button>
                        <img
                        src="{% if deal.talent.user.avatar %}{{ deal.talent.user.avatar.url }}{% else %}{% static 'vendors/images/default_avatar.png' %}{% endif %}"
                        alt="{{ deal.talent.user.name_short }}"
                        class="w-14 h-14 rounded-full object-cover">

                        <div>
                            <div class="text-lg font-bold">
                                {{ deal.talent.user.name_short }}
                            </div>
                            <div class="text-gray-500 text-sm font-medium">
                                <a href="{% url "jobs:my_detail" deal.job_id %}" data-turbo="false">
                                    {% if deal.job.title|length < 70 %}
                                        {{ deal.job.title }}
                                    {% else %}
                                        {{ deal.job.title|slice:":70" }}...
                                    {% endif %}
                                </a>
                            </div>
                        </div>                    
                    </a>
                    {% else %}
                    <!--if user = talent-->
                    <div>
                        <button type="button" data-action="click->chat#hideMessages" class="md:hidden">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" class="w-6 h-6 text-base-content/60">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18"></path>
                            </svg>
                        </button>
                        <img
                        src="{% if deal.client.user.avatar %}{{ deal.client.user.avatar.url }}{% else %}{% static 'vendors/images/default_avatar.png' %}{% endif %}"
                        alt="{{ deal.client.user.name_short }}"
                        class="w-14 h-14 rounded-full object-cover">

                        <div>
                            <div class="text-lg font-bold">
                                {{ deal.client.user.name_short }}
                            </div>
                            <div class="text-gray-500 text-sm font-medium">
                                <a href="{% url "deals:talent_deal_detail" deal.id %}" data-turbo="false">
                                    {% if deal.job.title|length < 70 %}
                                        {{ deal.job.title }}
                                    {% else %}
                                        {{ deal.job.title|slice:":70" }}...
                                    {% endif %}
                                </a>
                            </div>
                        </div>                    
                    </div>
                    {% endif %}
                    </div>
                    <div class="w-full sm:w-auto">
                        {% if user == deal.client.user %}
                        {% include "deals/components/hiring_create_control.html" with  deal=deal %}
                    {% endif %}
                    </div>
                </div>
            </div>

            <div id="loadmore">
                {% if page_obj.has_next %}
                    {% include 'chat/components/loadmore.html' with page_obj=page_obj deal=deal only %}
                {% endif %}
            </div>

            <div id="my-deal-messages" data-scroll-to-down-target="list"
                 class="flex flex-col space-y-4 p-3 bg-white overflow-y-auto scrollbar-thumb-blue scrollbar-thumb-rounded scrollbar-track-blue-lighter scrollbar-w-2 scrolling-touch">
                {% include 'chat/components/message_list.html' with chat_messages=chat_messages %}
            </div>

            <div data-chat-target="formWrap" class="border-t border-gray-300 p-4 h-[180px]">
                <turbo-frame id="chat-messages-create" src="{% url "chat:message_create" deal.id %}">

                    <div class="h-[180px] w-full">{% include 'components/preloader.html'%}</div>

                </turbo-frame>
            </div>
        </div>
    </turbo-frame>


{% endblock %}