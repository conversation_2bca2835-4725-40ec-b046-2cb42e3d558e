# Exercise 3: Notification Center 🔔

## 🎯 Objective

Build a comprehensive notification system with real-time updates, user preferences, multiple delivery channels, and an intuitive notification center interface. This exercise focuses on WebSocket integration, user experience design, and managing complex state.

## 📋 Prerequisites

- Completed Lessons 1-18 (Setup through Chat System)
- Understanding of WebSockets and real-time communication
- Knowledge of Django Channels and Redis
- Familiarity with JavaScript event handling

## 🎨 What You'll Build

### **Notification Center Features**
- Real-time notification dropdown
- Mark as read/unread functionality
- Notification categories and filtering
- Bulk actions (mark all as read, delete)
- Notification preferences management
- Push notification support

### **Real-time Updates**
- WebSocket connection for live notifications
- Toast notifications for immediate feedback
- Badge counters for unread notifications
- Sound notifications (optional)
- Browser notification API integration

## 🛠️ Implementation Steps

### **Step 1: Enhanced Notification Models**

```python
# backend/apps/notifications/models.py
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
import uuid

User = get_user_model()

class NotificationCategory(models.Model):
    name = models.CharField(max_length=50, unique=True)
    display_name = models.CharField(max_length=100)
    icon = models.CharField(max_length=50)  # CSS class or icon name
    color = models.CharField(max_length=20, default='blue')
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name_plural = "Notification Categories"
    
    def __str__(self):
        return self.display_name

class Notification(models.Model):
    class Priority(models.TextChoices):
        LOW = 'low', 'Low'
        NORMAL = 'normal', 'Normal'
        HIGH = 'high', 'High'
        URGENT = 'urgent', 'Urgent'
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    category = models.ForeignKey(NotificationCategory, on_delete=models.CASCADE)
    
    # Content
    title = models.CharField(max_length=255)
    message = models.TextField()
    action_url = models.URLField(blank=True)
    action_text = models.CharField(max_length=50, blank=True)
    
    # Metadata
    priority = models.CharField(max_length=10, choices=Priority.choices, default=Priority.NORMAL)
    data = models.JSONField(default=dict)  # Additional context data
    
    # Status
    is_read = models.BooleanField(default=False)
    is_archived = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', 'is_read', 'created_at']),
            models.Index(fields=['category', 'created_at']),
            models.Index(fields=['priority', 'created_at']),
        ]
    
    def mark_as_read(self):
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=['is_read', 'read_at'])
    
    def mark_as_unread(self):
        if self.is_read:
            self.is_read = False
            self.read_at = None
            self.save(update_fields=['is_read', 'read_at'])

class NotificationPreference(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    category = models.ForeignKey(NotificationCategory, on_delete=models.CASCADE)
    
    # Delivery preferences
    email_enabled = models.BooleanField(default=True)
    push_enabled = models.BooleanField(default=True)
    in_app_enabled = models.BooleanField(default=True)
    
    # Frequency settings
    email_frequency = models.CharField(
        max_length=20,
        choices=[
            ('immediate', 'Immediate'),
            ('daily', 'Daily Digest'),
            ('weekly', 'Weekly Digest'),
            ('never', 'Never')
        ],
        default='immediate'
    )
    
    class Meta:
        unique_together = ['user', 'category']

# Notification service
class NotificationService:
    @staticmethod
    def create_notification(recipient, category_name, title, message, **kwargs):
        """Create a new notification"""
        try:
            category = NotificationCategory.objects.get(name=category_name)
        except NotificationCategory.DoesNotExist:
            category = NotificationCategory.objects.create(
                name=category_name,
                display_name=category_name.replace('_', ' ').title()
            )
        
        notification = Notification.objects.create(
            recipient=recipient,
            category=category,
            title=title,
            message=message,
            **kwargs
        )
        
        # Send real-time notification
        from .consumers import send_notification_to_user
        send_notification_to_user(recipient.id, notification)
        
        return notification
    
    @staticmethod
    def mark_all_as_read(user, category=None):
        """Mark all notifications as read for a user"""
        queryset = user.notifications.filter(is_read=False)
        if category:
            queryset = queryset.filter(category=category)
        
        queryset.update(is_read=True, read_at=timezone.now())
    
    @staticmethod
    def get_unread_count(user, category=None):
        """Get unread notification count"""
        queryset = user.notifications.filter(is_read=False, is_archived=False)
        if category:
            queryset = queryset.filter(category=category)
        
        return queryset.count()
```

### **Step 2: WebSocket Consumer for Real-time Notifications**

```python
# backend/apps/notifications/consumers.py
import json
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from .models import Notification

User = get_user_model()

class NotificationConsumer(AsyncWebsocketConsumer):
    async def connect(self):
        if self.scope["user"].is_anonymous:
            await self.close()
            return
        
        self.user_id = self.scope["user"].id
        self.group_name = f"notifications_{self.user_id}"
        
        # Join notification group
        await self.channel_layer.group_add(
            self.group_name,
            self.channel_name
        )
        
        await self.accept()
        
        # Send initial unread count
        unread_count = await self.get_unread_count()
        await self.send(text_data=json.dumps({
            'type': 'unread_count',
            'count': unread_count
        }))
    
    async def disconnect(self, close_code):
        if hasattr(self, 'group_name'):
            await self.channel_layer.group_discard(
                self.group_name,
                self.channel_name
            )
    
    async def receive(self, text_data):
        data = json.loads(text_data)
        message_type = data.get('type')
        
        if message_type == 'mark_as_read':
            await self.mark_notification_as_read(data.get('notification_id'))
        elif message_type == 'mark_all_as_read':
            await self.mark_all_as_read()
        elif message_type == 'get_notifications':
            await self.send_notifications(data.get('page', 1))
    
    # WebSocket message handlers
    async def notification_message(self, event):
        """Send notification to WebSocket"""
        await self.send(text_data=json.dumps({
            'type': 'new_notification',
            'notification': event['notification']
        }))
    
    async def unread_count_update(self, event):
        """Send updated unread count"""
        await self.send(text_data=json.dumps({
            'type': 'unread_count',
            'count': event['count']
        }))
    
    # Database operations
    @database_sync_to_async
    def get_unread_count(self):
        return self.scope["user"].notifications.filter(
            is_read=False, 
            is_archived=False
        ).count()
    
    @database_sync_to_async
    def mark_notification_as_read(self, notification_id):
        try:
            notification = self.scope["user"].notifications.get(id=notification_id)
            notification.mark_as_read()
            return True
        except Notification.DoesNotExist:
            return False
    
    @database_sync_to_async
    def mark_all_as_read(self):
        self.scope["user"].notifications.filter(is_read=False).update(
            is_read=True,
            read_at=timezone.now()
        )

# Helper function to send notifications
def send_notification_to_user(user_id, notification):
    """Send notification to user's WebSocket group"""
    from channels.layers import get_channel_layer
    from asgiref.sync import async_to_sync
    
    channel_layer = get_channel_layer()
    group_name = f"notifications_{user_id}"
    
    notification_data = {
        'id': str(notification.id),
        'title': notification.title,
        'message': notification.message,
        'category': {
            'name': notification.category.name,
            'display_name': notification.category.display_name,
            'icon': notification.category.icon,
            'color': notification.category.color
        },
        'priority': notification.priority,
        'is_read': notification.is_read,
        'action_url': notification.action_url,
        'action_text': notification.action_text,
        'created_at': notification.created_at.isoformat()
    }
    
    async_to_sync(channel_layer.group_send)(
        group_name,
        {
            'type': 'notification_message',
            'notification': notification_data
        }
    )
```

### **Step 3: Notification Center Views**

```python
# backend/apps/notifications/views.py
from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.views.decorators.http import require_POST
from .models import Notification, NotificationCategory, NotificationService

@login_required
def notification_center(request):
    """Main notification center view"""
    categories = NotificationCategory.objects.filter(is_active=True)
    
    # Get filter parameters
    category_filter = request.GET.get('category')
    status_filter = request.GET.get('status', 'all')  # all, unread, read
    
    # Build queryset
    notifications = request.user.notifications.filter(is_archived=False)
    
    if category_filter:
        notifications = notifications.filter(category__name=category_filter)
    
    if status_filter == 'unread':
        notifications = notifications.filter(is_read=False)
    elif status_filter == 'read':
        notifications = notifications.filter(is_read=True)
    
    # Pagination
    paginator = Paginator(notifications.select_related('category'), 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Statistics
    stats = {
        'total': request.user.notifications.filter(is_archived=False).count(),
        'unread': request.user.notifications.filter(is_read=False, is_archived=False).count(),
        'read': request.user.notifications.filter(is_read=True, is_archived=False).count(),
    }
    
    context = {
        'notifications': page_obj,
        'categories': categories,
        'stats': stats,
        'current_category': category_filter,
        'current_status': status_filter,
    }
    
    return render(request, 'notifications/center.html', context)

@require_POST
@login_required
def mark_as_read(request, notification_id):
    """Mark single notification as read"""
    notification = get_object_or_404(
        request.user.notifications, 
        id=notification_id
    )
    
    notification.mark_as_read()
    
    return JsonResponse({
        'success': True,
        'unread_count': NotificationService.get_unread_count(request.user)
    })
```

## ✅ Expected Outcome

After completing this exercise, you should have:

1. **Real-time Notification Center**: Live updates without page refresh
2. **Comprehensive Management**: Read/unread states, bulk actions, filtering
3. **Multiple Notification Types**: Toast, browser, and in-app notifications
4. **User Preferences**: Customizable notification settings
5. **WebSocket Integration**: Real-time communication with the server
6. **Responsive Design**: Works seamlessly across all devices

## 🚀 Extension Challenges

1. **Push Notifications**: Implement service worker for offline notifications
2. **Email Digests**: Create daily/weekly notification summaries
3. **Smart Grouping**: Group related notifications together
4. **Notification Templates**: Create reusable notification templates
5. **Analytics Dashboard**: Track notification engagement and effectiveness

## 💡 Solution Tips

- Implement proper WebSocket reconnection logic
- Use efficient database queries with proper indexing
- Add proper error handling for network issues
- Consider implementing notification batching for high-volume scenarios
- Use CSS transitions for smooth animations

Outstanding work! You've built a sophisticated notification system that provides users with real-time updates and comprehensive management tools.

**Next**: [Exercise 4: Analytics Dashboard](./exercise-4-analytics.md)
