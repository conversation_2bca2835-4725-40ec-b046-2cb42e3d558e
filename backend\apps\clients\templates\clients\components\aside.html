<div class="sidebar mb-4 bg-white w-[250px] h-screen lg:h-[calc(100vh-156px)] left-0 top-0 lg:top-[2px] fixed lg:absolute z-[102] lg:z-10 p-6 lg:pt-0 pl-0 transition-transform -translate-x-full lg:translate-x-0 w-80 pl-3"
     data-aside-target="container">
    <button type="button" data-action="click->aside#animateHide"
            class="text-dark-gray-400 bg-transparent hover:text-gray-500 rounded-md text-sm p-1.5 ml-auto inline-flex items-center absolute top-0 right-0 lg:hidden">
        <svg aria-hidden="true" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"
             xmlns="http://www.w3.org/2000/svg">
            <path data-aside-target="close" fill-rule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clip-rule="evenodd"></path>
        </svg>
        <span class="sr-only">Hide aside</span>
    </button>

    <ul class="relative lg:before:content-[''] lg:before:absolute before:w-[3px] lg:before:h-full lg:before:rounded lg:before:bg-[#e6e9ec]">
        <li {% if request.path == '/clients/profile/' or request.path == '/clients/create/' %} class="active" {% endif %}>
            <a class="block text-base font-medium py-1 px-3" href="{% url "clients:profile" %}">Profile Settings</a>
        </li>
        <li {% if request.path == '/clients/password_change/' %} class="active" {% endif %}>
            <a class="block text-base font-medium py-1 px-3" href="{% url "client_password_change" %}">Change password</a>
        </li>
        <li {% if request.path == '/finance/clientspm/' %} class="active" {% endif %}>
            <a class="block text-base font-medium py-1 px-3" href="{% url "finance:client_pm" %}">Payment details</a>
        </li>
        <li {% if request.path == '/finance/clients' %} class="active" {% endif %}>
            <a class="block text-base font-medium py-1 px-3" href="{% url "finance:client_operations" %}">Transactions history</a>
        </li>
{#        {% if not user.is_talent %}#}
{#            <li {% if request.path == '/talents/create/' %} class="active" {% endif %}>#}
{#                <a class="block text-base font-medium py-1 px-3" href="{% url "talents:create" %}">#}
{#                    Create crelancer's Profile#}
{#                </a>#}
{#            </li>#}
{#        {% endif %}#}
        <li {% if request.path == '/account-delete/' %} class="active" {% endif %}>
            <a class="block text-base font-medium py-1 px-3" href="{% url "account_delete" %}">Account delete</a>
        </li>
    </ul>
</div>