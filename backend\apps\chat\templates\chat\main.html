{% extends "base.html" %}
{% load static %}
{% load turbo_streams %}


{% block extra_js %}
    <script src="{% static "turbo/js/reconnecting-websocket.min.js" %}" defer="" data-turbo-track="reload"></script>
    <script src="{% static "turbo/js/turbo-django.js" %}" defer="" data-turbo-track="reload"></script>
{% endblock %}


{% block content %}
    {% turbo_subscribe user %}
    
    <!--begin messenger wrap-->
    <div class="container px-0 md:py-3 md:px-4 overflow-hidden">
        <div id="chat" data-controller="chat" data-chat-value-is-open="false" data-current-user-value = "{{user.id}}" data-action="resize@window->chat#clearStyles" class="w-[calc(100vw*2)] md:w-full flex flex-nowrap justify-between relative h-[calc(100vh-100px)] md:h-[calc(100vh-124px)] border border-gray-300 md:rounded-xl transition-transform">               
            <div class="w-screen md:w-[300px] lg:w-[420px] h-full md:absolute md:top-0 md:left-0 border-r border-gray-300 z-20">
                <turbo-frame id="my-deals-list" src="{% url 'deals:my_list_frame' %}" target="chat-messages">
                    <div class="w-full">{% include 'components/preloader.html'%}</div>
                </turbo-frame>
            </div>
            <div class="w-screen md:w-full h-full p-0 md:pl-[300px] lg:pl-[420px] md:absolute md:top-0 md:right-0 z-10">                
            {% block messages %}
                <turbo-frame id="chat-messages">
                    <div class="justify-between flex flex-col h-[calc(100vh-100px)] md:h-[calc(100vh-124px)]">
                        <div class="relative v-full h-full flex items-center justify-center">
                            <div class="flex flex-col items-center text-center">
                                <img src="{% static 'vendors/images/empty_search_result.png' %}" alt="Select a room to display messages" class="w-full max-w-[420px] h-auto">
                                <h2 class="text-xl font-bold mb-2">Select a room to display messages.</h2>
                            </div>
                        </div>
                    </div>
                </turbo-frame>
            {% endblock %}            
            </div>        
        </div>
    </div>
    <!--end messenger wrap-->
{% endblock %}