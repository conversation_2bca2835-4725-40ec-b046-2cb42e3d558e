# Exercise 2: Advanced Job Search 🔍

## 🎯 Objective

Build a sophisticated job search system with multiple filters, sorting options, real-time results, and advanced query capabilities. This exercise will help you practice complex database queries, AJAX interactions, and creating intuitive user interfaces.

## 📋 Prerequisites

- Completed Lessons 1-13 (Setup, Backend, and Frontend Development)
- Understanding of Django QuerySets and database optimization
- Basic knowledge of JavaScript and AJAX
- Familiarity with Stimulus controllers

## 🎨 What You'll Build

### **Search Features**
- Text search across job titles and descriptions
- Category and skill-based filtering
- Budget range filtering
- Location-based search
- Date range filtering
- Experience level requirements
- Project duration filtering

### **Advanced Functionality**
- Real-time search results (no page refresh)
- Search suggestions and autocomplete
- Saved searches
- Search result sorting
- Pagination with infinite scroll
- Search analytics

## 🛠️ Implementation Steps

### **Step 1: Enhanced Job Model and Search Backend**

```python
# backend/apps/jobs/search.py
from django.db.models import Q, Count, Avg
from django.contrib.postgres.search import SearchVector, SearchQuery, SearchRank
from .models import Job

class JobSearchEngine:
    def __init__(self, queryset=None):
        self.queryset = queryset or Job.objects.published()
    
    def search(self, query_params):
        """Main search method that applies all filters"""
        queryset = self.queryset
        
        # Text search
        if query_params.get('q'):
            queryset = self._apply_text_search(queryset, query_params['q'])
        
        # Category filter
        if query_params.get('category'):
            queryset = queryset.filter(category__slug=query_params['category'])
        
        # Skills filter
        if query_params.getlist('skills'):
            queryset = queryset.filter(skills__id__in=query_params.getlist('skills')).distinct()
        
        # Budget range
        if query_params.get('min_budget'):
            queryset = queryset.filter(budget_min__gte=query_params['min_budget'])
        if query_params.get('max_budget'):
            queryset = queryset.filter(budget_max__lte=query_params['max_budget'])
        
        # Location
        if query_params.get('location'):
            queryset = self._apply_location_filter(queryset, query_params['location'])
        
        # Experience level
        if query_params.get('experience_level'):
            queryset = self._apply_experience_filter(queryset, query_params['experience_level'])
        
        # Project duration
        if query_params.get('duration'):
            queryset = self._apply_duration_filter(queryset, query_params['duration'])
        
        # Date range
        if query_params.get('posted_since'):
            queryset = self._apply_date_filter(queryset, query_params['posted_since'])
        
        # Apply sorting
        queryset = self._apply_sorting(queryset, query_params.get('sort', 'relevance'))
        
        return queryset
    
    def _apply_text_search(self, queryset, search_term):
        """Apply full-text search using PostgreSQL"""
        search_vector = SearchVector('title', weight='A') + SearchVector('description', weight='B')
        search_query = SearchQuery(search_term)
        
        return queryset.annotate(
            search=search_vector,
            rank=SearchRank(search_vector, search_query)
        ).filter(search=search_query).order_by('-rank')
    
    def _apply_location_filter(self, queryset, location):
        """Filter by location (remote, specific city, etc.)"""
        if location.lower() == 'remote':
            return queryset.filter(is_remote=True)
        else:
            return queryset.filter(
                Q(location__icontains=location) | Q(is_remote=True)
            )
    
    def _apply_experience_filter(self, queryset, experience_level):
        """Filter by required experience level"""
        experience_mapping = {
            'entry': Q(experience_required__lte=1),
            'intermediate': Q(experience_required__gte=2, experience_required__lte=5),
            'senior': Q(experience_required__gte=5),
        }
        
        if experience_level in experience_mapping:
            return queryset.filter(experience_mapping[experience_level])
        
        return queryset
    
    def _apply_duration_filter(self, queryset, duration):
        """Filter by project duration"""
        duration_mapping = {
            'short': Q(duration_weeks__lte=4),
            'medium': Q(duration_weeks__gte=4, duration_weeks__lte=12),
            'long': Q(duration_weeks__gte=12),
        }
        
        if duration in duration_mapping:
            return queryset.filter(duration_mapping[duration])
        
        return queryset
    
    def _apply_date_filter(self, queryset, posted_since):
        """Filter by posting date"""
        from datetime import datetime, timedelta
        
        date_mapping = {
            'today': timedelta(days=1),
            'week': timedelta(weeks=1),
            'month': timedelta(days=30),
        }
        
        if posted_since in date_mapping:
            cutoff_date = datetime.now() - date_mapping[posted_since]
            return queryset.filter(created_at__gte=cutoff_date)
        
        return queryset
    
    def _apply_sorting(self, queryset, sort_option):
        """Apply sorting to results"""
        sort_mapping = {
            'relevance': '-created_at',  # Default if no text search
            'newest': '-created_at',
            'oldest': 'created_at',
            'budget_high': '-budget_max',
            'budget_low': 'budget_min',
            'applications': '-applications_count',
        }
        
        # Annotate with applications count if needed
        if sort_option == 'applications':
            queryset = queryset.annotate(applications_count=Count('deal'))
        
        return queryset.order_by(sort_mapping.get(sort_option, '-created_at'))
    
    def get_search_suggestions(self, query, limit=5):
        """Get search suggestions based on partial query"""
        suggestions = []
        
        # Job title suggestions
        title_matches = Job.objects.filter(
            title__icontains=query
        ).values_list('title', flat=True).distinct()[:limit]
        
        suggestions.extend([{'type': 'title', 'text': title} for title in title_matches])
        
        # Skill suggestions
        from taxonomy.models import Skill
        skill_matches = Skill.objects.filter(
            name__icontains=query
        ).values_list('name', flat=True)[:limit]
        
        suggestions.extend([{'type': 'skill', 'text': skill} for skill in skill_matches])
        
        return suggestions[:limit]
```

### **Step 2: Advanced Search Form and Frontend**

```html
<!-- backend/templates/jobs/search.html -->
{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}Search Jobs - Crelancer{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Search Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Find Your Perfect Job</h1>
        <p class="text-gray-600 mt-2">{{ total_results }} jobs found</p>
    </div>

    <!-- Search Form -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8"
         data-controller="job-search"
         data-job-search-url-value="{% url 'jobs:search_ajax' %}"
         data-job-search-suggestions-url-value="{% url 'jobs:search_suggestions' %}">
        
        <form data-job-search-target="form" data-action="submit->job-search#search">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Text Search -->
                <div class="lg:col-span-2 relative">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">
                        Search Jobs
                    </label>
                    <div class="relative">
                        <input type="text" 
                               name="q" 
                               id="search"
                               value="{{ search_params.q }}"
                               placeholder="Job title, skills, or keywords..."
                               class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                               data-job-search-target="searchInput"
                               data-action="input->job-search#handleSearchInput keydown->job-search#handleKeydown">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                        
                        <!-- Search Suggestions -->
                        <div data-job-search-target="suggestions" 
                             class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 hidden">
                        </div>
                    </div>
                </div>

                <!-- Category Filter -->
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-1">
                        Category
                    </label>
                    <select name="category" 
                            id="category"
                            class="block w-full py-2 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            data-action="change->job-search#search">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                        <option value="{{ category.slug }}" 
                                {% if search_params.category == category.slug %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Location Filter -->
                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700 mb-1">
                        Location
                    </label>
                    <input type="text" 
                           name="location" 
                           id="location"
                           value="{{ search_params.location }}"
                           placeholder="City or 'Remote'"
                           class="block w-full py-2 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                           data-action="input->job-search#debouncedSearch">
                </div>
            </div>
        </form>
    </div>

    <!-- Results Section -->
    <div class="flex flex-col lg:flex-row gap-8">
        <!-- Results -->
        <div class="flex-1">
            <!-- Loading State -->
            <div data-job-search-target="loading" class="hidden">
                <div class="flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span class="ml-2 text-gray-600">Searching...</span>
                </div>
            </div>

            <!-- Results Container -->
            <div data-job-search-target="results">
                {% include 'jobs/search_results.html' %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
```

## ✅ Expected Outcome

After completing this exercise, you should have:

1. **Advanced Search Engine**: Sophisticated filtering and sorting capabilities
2. **Real-time Results**: Search updates without page refreshes
3. **Search Suggestions**: Autocomplete functionality for better UX
4. **Multiple Filter Types**: Text, category, skills, budget, location, etc.
5. **Responsive Design**: Works well on all device sizes
6. **Performance Optimized**: Efficient database queries and caching

## 🚀 Extension Challenges

1. **Saved Searches**: Allow users to save and manage search queries
2. **Search Analytics**: Track popular searches and user behavior
3. **Elasticsearch Integration**: Implement full-text search with Elasticsearch
4. **Geolocation Search**: Add map-based location filtering
5. **Machine Learning**: Implement personalized search recommendations

## 💡 Solution Tips

- Use database indexes for frequently searched fields
- Implement proper pagination to handle large result sets
- Add debouncing to prevent excessive API calls
- Use browser history API for back/forward navigation
- Consider implementing search result caching

Excellent work! You've built a sophisticated search system that provides users with powerful tools to find exactly what they're looking for.

**Next**: [Exercise 3: Notification Center](./exercise-3-notifications.md)
