# Lesson 10: Hotwire (Turbo + Stimulus) Fundamentals ⚡

## 🎯 Learning Objectives
By the end of this lesson, you will understand:
- What Hotwire is and why it's used in Crelancer
- Turbo Drive, Frames, and Streams
- Stimulus controllers and their lifecycle
- How to create interactive features without complex JavaScript
- Integration patterns with Django

## 🌟 What is Hotwire?

**Hotwire** (HTML Over The Wire) is a modern approach to building web applications that sends HTML instead of JSON over the wire. It consists of:

### **Core Components**
1. **Turbo Drive**: Fast page navigation
2. **Turbo Frames**: Partial page updates
3. **Turbo Streams**: Real-time updates
4. **Stimulus**: JavaScript sprinkles for behavior

### **Why Hotwire in Crelancer?**
```
✅ Server-side rendering (better SEO)
✅ Minimal JavaScript complexity
✅ Fast, SPA-like experience
✅ Progressive enhancement
✅ Easy Django integration
```

## 🚗 Turbo Drive - Fast Navigation

### **How Turbo Drive Works**
```javascript
// Automatic - no code needed!
// Turbo intercepts link clicks and form submissions
// Replaces page content without full reload
// Maintains browser history and back button

// Example: Normal link becomes Turbo-powered
<a href="/jobs/">Browse Jobs</a>
// ↓ Turbo automatically converts to AJAX navigation
```

### **Turbo Drive Configuration**
```javascript
// frontend/src/application/turbo.js
import { Turbo } from "@hotwired/turbo-rails"

// Configure Turbo Drive
Turbo.session.drive = true

// Disable Turbo for specific links
document.addEventListener("turbo:before-visit", (event) => {
  if (event.detail.url.includes("/admin/")) {
    event.preventDefault()
    window.location.href = event.detail.url
  }
})

// Handle loading states
document.addEventListener("turbo:before-visit", () => {
  document.body.classList.add("turbo-loading")
})

document.addEventListener("turbo:visit", () => {
  document.body.classList.remove("turbo-loading")
})
```

### **Turbo Drive in Templates**
```html
<!-- Disable Turbo for specific links -->
<a href="/external-site" data-turbo="false">External Link</a>

<!-- Force full page reload -->
<a href="/admin/" data-turbo-method="get" data-turbo="false">Admin Panel</a>

<!-- Turbo-powered form -->
<form action="{% url 'jobs:create' %}" method="post">
  {% csrf_token %}
  <!-- Form fields -->
  <button type="submit">Create Job</button>
</form>
```

## 🖼️ Turbo Frames - Partial Updates

### **Basic Turbo Frame Usage**
```html
<!-- jobs/job_list.html -->
<div class="job-filters">
  <turbo-frame id="job-filters">
    <form action="{% url 'jobs:list' %}" method="get">
      <select name="category" onchange="this.form.submit()">
        <option value="">All Categories</option>
        {% for category in categories %}
        <option value="{{ category.slug }}">{{ category.name }}</option>
        {% endfor %}
      </select>
    </form>
  </turbo-frame>
</div>

<div class="job-results">
  <turbo-frame id="job-results">
    {% include 'jobs/job_results_frame.html' %}
  </turbo-frame>
</div>
```

### **Turbo Frame Targeting**
```html
<!-- Target different frames -->
<turbo-frame id="job-filters">
  <form action="{% url 'jobs:search' %}" 
        method="get" 
        data-turbo-frame="job-results">
    <!-- This form will update job-results frame -->
    <input type="text" name="q" placeholder="Search jobs...">
    <button type="submit">Search</button>
  </form>
</turbo-frame>

<!-- Lazy loading frames -->
<turbo-frame id="job-applications" 
             src="{% url 'jobs:applications' job.id %}"
             loading="lazy">
  <div class="loading-spinner">Loading applications...</div>
</turbo-frame>
```

### **Django Views for Turbo Frames**
```python
# jobs/views.py
class JobSearchFrameView(ListView):
    """
    Return job search results for Turbo Frame
    """
    model = Job
    template_name = "jobs/search_results_frame.html"
    context_object_name = "jobs"
    
    def get_queryset(self):
        queryset = Job.objects.published()
        
        # Search functionality
        query = self.request.GET.get('q')
        if query:
            queryset = queryset.filter(
                Q(title__icontains=query) | 
                Q(description__icontains=query)
            )
        
        # Category filter
        category = self.request.GET.get('category')
        if category:
            queryset = queryset.filter(category__slug=category)
        
        return queryset.select_related('client__user', 'category')
    
    def get_template_names(self):
        # Return frame template for Turbo requests
        if self.request.headers.get('Turbo-Frame'):
            return ['jobs/search_results_frame.html']
        return ['jobs/job_list.html']
```

## 📡 Turbo Streams - Real-time Updates

### **Turbo Stream Actions**
```html
<!-- Turbo Stream template: chat/new_message.turbo_stream.html -->
{% load turbo_stream %}

<!-- Append new message to chat -->
{% turbo_stream_append "chat-messages" %}
  {% include 'chat/message_item.html' with message=message %}
{% endturbo_stream_append %}

<!-- Update unread count -->
{% turbo_stream_replace "unread-count" %}
  <span id="unread-count" class="badge">{{ unread_count }}</span>
{% endturbo_stream_replace %}

<!-- Clear message form -->
{% turbo_stream_replace "message-form" %}
  <form id="message-form" action="{% url 'chat:send_message' deal.id %}" method="post">
    {% csrf_token %}
    <input type="text" name="content" placeholder="Type a message...">
    <button type="submit">Send</button>
  </form>
{% endturbo_stream_replace %}
```

### **Django Views for Turbo Streams**
```python
# chat/views.py
from turbo_response import TurboStream

class SendMessageView(LoginRequiredMixin, View):
    """
    Send chat message and return Turbo Stream response
    """
    
    def post(self, request, deal_id):
        deal = get_object_or_404(Deal, id=deal_id)
        
        # Check permissions
        if not (request.user == deal.client.user or 
                request.user == deal.talent.user):
            return HttpResponseForbidden()
        
        # Create message
        content = request.POST.get('content', '').strip()
        if content:
            message = Message.objects.create(
                deal=deal,
                sender=request.user,
                content=content
            )
            
            # Return Turbo Stream response
            return TurboStream("chat-messages").append.response(
                "chat/message_item.html",
                {"message": message}
            )
        
        return HttpResponseBadRequest()

class JobApplicationView(TalentRequiredMixin, View):
    """
    Apply for job and return Turbo Stream response
    """
    
    def post(self, request, job_id):
        job = get_object_or_404(Job, id=job_id)
        
        # Check if already applied
        if Deal.objects.filter(job=job, talent=request.user.talent).exists():
            return TurboStream("job-actions").replace.response(
                "jobs/already_applied.html",
                {"job": job}
            )
        
        # Create application
        deal = Deal.objects.create(
            job=job,
            talent=request.user.talent,
            proposal=request.POST.get('proposal', '')
        )
        
        # Return multiple stream actions
        return TurboStream([
            TurboStream("job-actions").replace.template(
                "jobs/application_success.html",
                {"job": job, "deal": deal}
            ),
            TurboStream("applications-count").replace.template(
                "jobs/applications_count.html",
                {"count": job.deal_set.count()}
            )
        ]).response()
```

## 🎮 Stimulus Controllers

### **Basic Stimulus Controller**
```javascript
// frontend/src/controllers/dropdown_controller.js
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["menu", "button"]
  static classes = ["open"]
  
  connect() {
    // Called when controller connects to DOM
    console.log("Dropdown controller connected")
    this.close()
  }
  
  disconnect() {
    // Called when controller disconnects from DOM
    console.log("Dropdown controller disconnected")
  }
  
  toggle(event) {
    event.preventDefault()
    
    if (this.menuTarget.classList.contains(this.openClass)) {
      this.close()
    } else {
      this.open()
    }
  }
  
  open() {
    this.menuTarget.classList.add(this.openClass)
    this.buttonTarget.setAttribute("aria-expanded", "true")
    
    // Close on outside click
    document.addEventListener("click", this.closeOnOutsideClick.bind(this))
  }
  
  close() {
    this.menuTarget.classList.remove(this.openClass)
    this.buttonTarget.setAttribute("aria-expanded", "false")
    
    // Remove outside click listener
    document.removeEventListener("click", this.closeOnOutsideClick.bind(this))
  }
  
  closeOnOutsideClick(event) {
    if (!this.element.contains(event.target)) {
      this.close()
    }
  }
}
```

### **Using Stimulus Controller in HTML**
```html
<!-- Dropdown component -->
<div class="relative" 
     data-controller="dropdown"
     data-dropdown-open-class="block">
  
  <button data-dropdown-target="button"
          data-action="click->dropdown#toggle"
          class="btn btn-primary">
    Menu
    <svg class="w-4 h-4 ml-2" fill="currentColor" viewBox="0 0 20 20">
      <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
    </svg>
  </button>
  
  <div data-dropdown-target="menu"
       class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg">
    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</a>
    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Logout</a>
  </div>
</div>
```

### **Advanced Stimulus Controller - Chat**
```javascript
// frontend/src/controllers/chat_controller.js
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["messagesContainer", "messageInput", "sendButton"]
  static values = { 
    dealId: Number,
    userRole: String,
    autoScroll: Boolean
  }
  
  connect() {
    this.scrollToBottom()
    this.setupAutoScroll()
    this.focusInput()
  }
  
  // Auto-scroll to bottom when new messages arrive
  messagesContainerTargetConnected() {
    if (this.autoScrollValue) {
      this.scrollToBottom()
    }
  }
  
  // Handle message form submission
  sendMessage(event) {
    event.preventDefault()
    
    const content = this.messageInputTarget.value.trim()
    if (!content) return
    
    // Disable form while sending
    this.messageInputTarget.disabled = true
    this.sendButtonTarget.disabled = true
    
    // Submit form via Turbo
    const form = event.target
    const formData = new FormData(form)
    
    fetch(form.action, {
      method: 'POST',
      body: formData,
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'Accept': 'text/vnd.turbo-stream.html'
      }
    })
    .then(response => response.text())
    .then(html => {
      // Turbo will handle the stream response
      this.clearInput()
      this.enableForm()
    })
    .catch(error => {
      console.error('Error sending message:', error)
      this.enableForm()
    })
  }
  
  // Handle typing indicators
  handleTyping() {
    clearTimeout(this.typingTimeout)
    
    // Show typing indicator
    this.showTypingIndicator()
    
    // Hide after 3 seconds of inactivity
    this.typingTimeout = setTimeout(() => {
      this.hideTypingIndicator()
    }, 3000)
  }
  
  // Scroll to bottom of messages
  scrollToBottom() {
    const container = this.messagesContainerTarget
    container.scrollTop = container.scrollHeight
  }
  
  // Setup auto-scroll behavior
  setupAutoScroll() {
    const container = this.messagesContainerTarget
    
    container.addEventListener('scroll', () => {
      const isAtBottom = container.scrollTop + container.clientHeight >= container.scrollHeight - 10
      this.autoScrollValue = isAtBottom
    })
  }
  
  // Helper methods
  clearInput() {
    this.messageInputTarget.value = ''
  }
  
  enableForm() {
    this.messageInputTarget.disabled = false
    this.sendButtonTarget.disabled = false
    this.focusInput()
  }
  
  focusInput() {
    this.messageInputTarget.focus()
  }
  
  showTypingIndicator() {
    // Implementation for typing indicator
  }
  
  hideTypingIndicator() {
    // Implementation for hiding typing indicator
  }
}
```

### **Job Search Controller**
```javascript
// frontend/src/controllers/job_search_controller.js
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["form", "results", "loading"]
  static values = { 
    url: String,
    debounceDelay: { type: Number, default: 300 }
  }
  
  connect() {
    this.timeout = null
  }
  
  // Debounced search
  search() {
    clearTimeout(this.timeout)
    
    this.timeout = setTimeout(() => {
      this.performSearch()
    }, this.debounceDelayValue)
  }
  
  performSearch() {
    const formData = new FormData(this.formTarget)
    const params = new URLSearchParams(formData)
    
    // Show loading state
    this.showLoading()
    
    // Fetch results
    fetch(`${this.urlValue}?${params}`, {
      headers: {
        'Accept': 'text/html',
        'Turbo-Frame': 'job-results'
      }
    })
    .then(response => response.text())
    .then(html => {
      this.resultsTarget.innerHTML = html
      this.hideLoading()
    })
    .catch(error => {
      console.error('Search error:', error)
      this.hideLoading()
    })
  }
  
  // Filter change handler
  filterChanged() {
    this.search()
  }
  
  // Clear filters
  clearFilters() {
    this.formTarget.reset()
    this.search()
  }
  
  showLoading() {
    this.loadingTarget.classList.remove('hidden')
    this.resultsTarget.classList.add('opacity-50')
  }
  
  hideLoading() {
    this.loadingTarget.classList.add('hidden')
    this.resultsTarget.classList.remove('opacity-50')
  }
}
```

## 🔧 Stimulus Application Setup

### **Application Controller**
```javascript
// frontend/src/application/stimulus.js
import { Application } from "@hotwired/stimulus"

// Import controllers
import DropdownController from "../controllers/dropdown_controller"
import ChatController from "../controllers/chat_controller"
import JobSearchController from "../controllers/job_search_controller"
import ModalController from "../controllers/modal_controller"
import TooltipController from "../controllers/tooltip_controller"

// Start Stimulus application
const application = Application.start()

// Register controllers
application.register("dropdown", DropdownController)
application.register("chat", ChatController)
application.register("job-search", JobSearchController)
application.register("modal", ModalController)
application.register("tooltip", TooltipController)

// Configure Stimulus development experience
application.debug = false
window.Stimulus = application

export { application }
```

### **Main Application Entry Point**
```javascript
// frontend/src/application.js
import "./application/turbo"
import "./application/stimulus"
import "./stylesheets/application.scss"

// Global JavaScript
document.addEventListener('DOMContentLoaded', function() {
  console.log('Crelancer application loaded')
})

// Handle Turbo events
document.addEventListener('turbo:load', function() {
  // Initialize any global functionality
  initializeTooltips()
  initializeModals()
})

function initializeTooltips() {
  // Initialize tooltip library if needed
}

function initializeModals() {
  // Initialize modal functionality if needed
}
```

## 🎯 Key Takeaways

1. **Hotwire Philosophy**: HTML over the wire for simpler, faster apps
2. **Turbo Drive**: Automatic fast navigation without configuration
3. **Turbo Frames**: Partial page updates for specific sections
4. **Turbo Streams**: Real-time updates with multiple actions
5. **Stimulus Controllers**: JavaScript behavior attached to HTML elements
6. **Progressive Enhancement**: Works without JavaScript, better with it

## 🔗 What's Next?

Now that you understand Hotwire fundamentals, let's explore how TailwindCSS creates beautiful, responsive designs.

**Next**: [Lesson 11: TailwindCSS Styling System](./11-tailwindcss.md)

---

## 💡 Quick Quiz

1. What are the three main components of Turbo?
2. How do Turbo Frames differ from Turbo Streams?
3. What's the purpose of Stimulus controllers?
4. How does Hotwire improve performance compared to traditional SPAs?

*Answers: 1) Drive, Frames, and Streams, 2) Frames replace sections, Streams perform specific actions, 3) Add JavaScript behavior to HTML elements, 4) Server-side rendering and minimal JavaScript*
