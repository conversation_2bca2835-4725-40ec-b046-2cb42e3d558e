# Lesson 8: Templates & Hotwire Integration 🎨

## 🎯 Learning Objectives
By the end of this lesson, you will understand:
- Django template system and inheritance
- Hotwire (Turbo + Stimulus) integration
- Component-based template design
- Dynamic content updates without page reloads
- Template optimization and best practices

## 🏗️ Template Architecture

### **Template Hierarchy**
```
templates/
├── 📄 base.html                    # Root template
├── 📁 layouts/                     # Layout templates
│   ├── 📄 app.html                # Main app layout
│   ├── 📄 auth.html               # Authentication layout
│   └── 📄 minimal.html            # Minimal layout
├── 📁 components/                  # Reusable components
│   ├── 📄 navbar.html
│   ├── 📄 job_card.html
│   ├── 📄 talent_card.html
│   └── 📄 pagination.html
├── 📁 forms/                       # Form templates
│   ├── 📄 field.html
│   ├── 📄 errors.html
│   └── 📄 buttons.html
└── 📁 [app_name]/                  # App-specific templates
    ├── 📄 list.html
    ├── 📄 detail.html
    └── 📄 form.html
```

### **Base Template Structure**
```html
<!-- base.html -->
<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Crelancer{% endblock %}</title>
    
    <!-- Hotwire Turbo -->
    <meta name="turbo-cache-control" content="no-cache">
    
    <!-- CSS -->
    {% load webpack_loader %}
    {% render_bundle 'app' 'css' %}
    
    <!-- Custom CSS -->
    {% block extra_css %}{% endblock %}
</head>
<body class="h-full bg-gray-50" data-controller="application">
    <!-- Navigation -->
    {% include 'components/navbar.html' %}
    
    <!-- Flash Messages -->
    {% include 'components/messages.html' %}
    
    <!-- Main Content -->
    <main class="flex-1">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    {% include 'components/footer.html' %}
    
    <!-- JavaScript -->
    {% render_bundle 'app' 'js' %}
    
    <!-- Custom JavaScript -->
    {% block extra_js %}{% endblock %}
</body>
</html>
```

### **App Layout Template**
```html
<!-- layouts/app.html -->
{% extends 'base.html' %}

{% block content %}
<div class="min-h-screen">
    <!-- Page Header -->
    {% block page_header %}
    <div class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <h1 class="text-3xl font-bold text-gray-900">
                    {% block page_title %}{% endblock %}
                </h1>
                {% block page_description %}{% endblock %}
            </div>
        </div>
    </div>
    {% endblock %}
    
    <!-- Main Content Area -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {% block app_content %}{% endblock %}
    </div>
</div>
{% endblock %}
```

## 🔄 Hotwire Integration

### **Turbo Frame Templates**
```html
<!-- jobs/job_list.html -->
{% extends 'layouts/app.html' %}

{% block page_title %}Find Jobs{% endblock %}

{% block app_content %}
<div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
    <!-- Filters Sidebar -->
    <div class="lg:col-span-1">
        <turbo-frame id="job-filters" src="{% url 'jobs:filters' %}">
            {% include 'jobs/filters_frame.html' %}
        </turbo-frame>
    </div>
    
    <!-- Job Results -->
    <div class="lg:col-span-3">
        <turbo-frame id="job-results" target="_top">
            {% include 'jobs/results_frame.html' %}
        </turbo-frame>
    </div>
</div>
{% endblock %}

<!-- jobs/results_frame.html -->
<turbo-frame id="job-results">
    <div class="space-y-6">
        <!-- Results Header -->
        <div class="flex justify-between items-center">
            <p class="text-gray-600">
                {{ jobs|length }} of {{ paginator.count }} jobs
            </p>
            
            <!-- Sort Options -->
            <select data-controller="sort" 
                    data-action="change->sort#update"
                    data-sort-url-value="{% url 'jobs:list' %}">
                <option value="newest">Newest First</option>
                <option value="budget_high">Highest Budget</option>
                <option value="budget_low">Lowest Budget</option>
            </select>
        </div>
        
        <!-- Job Cards -->
        <div class="space-y-4">
            {% for job in jobs %}
                {% include 'components/job_card.html' %}
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% include 'components/pagination.html' %}
    </div>
</turbo-frame>
```

### **Turbo Stream Templates**
```html
<!-- deals/hire_success.turbo_stream.html -->
{% load turbo_stream %}

<!-- Update deal status -->
{% turbo_stream_replace "deal-status-{{ deal.id }}" %}
    <div id="deal-status-{{ deal.id }}" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
        <strong>Hired!</strong> The talent has been hired for this job.
    </div>
{% endturbo_stream_replace %}

<!-- Update deal actions -->
{% turbo_stream_replace "deal-actions-{{ deal.id }}" %}
    <div id="deal-actions-{{ deal.id }}">
        <a href="{% url 'chat:message_list' deal.id %}" 
           class="btn btn-primary">
            Start Chat
        </a>
    </div>
{% endturbo_stream_replace %}

<!-- Show success notification -->
{% turbo_stream_prepend "notifications" %}
    <div class="alert alert-success" data-controller="auto-dismiss">
        Talent hired successfully! You can now start working together.
    </div>
{% endturbo_stream_prepend %}
```

### **Stimulus Controller Integration**
```html
<!-- chat/message_list.html -->
<div class="chat-container" 
     data-controller="chat"
     data-chat-deal-id-value="{{ deal.id }}"
     data-chat-user-role-value="{{ request.user.role }}">
    
    <!-- Messages Container -->
    <div class="messages-container" 
         data-chat-target="messagesContainer"
         data-action="scroll->chat#handleScroll">
        
        <turbo-frame id="chat-messages-{{ deal.id }}">
            {% for message in messages %}
                {% include 'chat/message_item.html' %}
            {% endfor %}
        </turbo-frame>
    </div>
    
    <!-- Message Form -->
    <turbo-frame id="message-form-{{ deal.id }}">
        <form data-controller="message-form"
              data-action="submit->message-form#submit"
              data-message-form-deal-id-value="{{ deal.id }}">
            {% csrf_token %}
            
            <div class="flex space-x-4">
                <input type="text" 
                       name="content"
                       placeholder="Type your message..."
                       data-message-form-target="input"
                       class="flex-1 rounded-lg border-gray-300">
                
                <button type="submit" 
                        data-message-form-target="submitButton"
                        class="btn btn-primary">
                    Send
                </button>
            </div>
        </form>
    </turbo-frame>
</div>
```

## 🧩 Component-Based Design

### **Reusable Job Card Component**
```html
<!-- components/job_card.html -->
<div class="job-card bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow"
     data-controller="job-card"
     data-job-card-id-value="{{ job.id }}">
    
    <!-- Job Header -->
    <div class="flex justify-between items-start mb-4">
        <div class="flex-1">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">
                <a href="{% url 'jobs:detail' job.pk %}" 
                   class="hover:text-blue-600">
                    {{ job.title }}
                </a>
            </h3>
            
            <div class="flex items-center text-sm text-gray-600 space-x-4">
                <span>{{ job.category.name }}</span>
                <span>{{ job.created_at|timesince }} ago</span>
                <span>{{ job.applications_count }} applications</span>
            </div>
        </div>
        
        <!-- Favorite Button -->
        {% if request.user.is_authenticated and request.user.role == 'talent' %}
        <button data-action="click->job-card#toggleFavorite"
                data-job-card-target="favoriteButton"
                class="text-gray-400 hover:text-red-500">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"/>
            </svg>
        </button>
        {% endif %}
    </div>
    
    <!-- Job Description -->
    <p class="text-gray-700 mb-4 line-clamp-3">
        {{ job.description|truncatewords:30 }}
    </p>
    
    <!-- Skills -->
    <div class="flex flex-wrap gap-2 mb-4">
        {% for skill in job.skills.all|slice:":5" %}
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {{ skill.name }}
        </span>
        {% endfor %}
        
        {% if job.skills.count > 5 %}
        <span class="text-sm text-gray-500">
            +{{ job.skills.count|add:"-5" }} more
        </span>
        {% endif %}
    </div>
    
    <!-- Budget and Actions -->
    <div class="flex justify-between items-center">
        <div class="text-lg font-semibold text-green-600">
            {{ job.get_budget_display }}
        </div>
        
        <div class="flex space-x-2">
            <a href="{% url 'jobs:detail' job.pk %}" 
               class="btn btn-outline btn-sm">
                View Details
            </a>
            
            {% if request.user.is_authenticated and request.user.role == 'talent' %}
            <button data-action="click->job-card#quickApply"
                    class="btn btn-primary btn-sm">
                Quick Apply
            </button>
            {% endif %}
        </div>
    </div>
</div>
```

### **Talent Profile Card Component**
```html
<!-- components/talent_card.html -->
<div class="talent-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
    <!-- Profile Header -->
    <div class="p-6">
        <div class="flex items-center space-x-4">
            <!-- Avatar -->
            <div class="flex-shrink-0">
                {% if talent.user.avatar %}
                <img src="{{ talent.user.avatar.url }}" 
                     alt="{{ talent.user.get_full_name }}"
                     class="w-16 h-16 rounded-full object-cover">
                {% else %}
                <div class="w-16 h-16 rounded-full bg-gray-300 flex items-center justify-center">
                    <span class="text-xl font-semibold text-gray-600">
                        {{ talent.user.first_name|first }}{{ talent.user.last_name|first }}
                    </span>
                </div>
                {% endif %}
            </div>
            
            <!-- Profile Info -->
            <div class="flex-1">
                <h3 class="text-lg font-semibold text-gray-900">
                    <a href="{% url 'talents:detail' talent.pk %}" 
                       class="hover:text-blue-600">
                        {{ talent.user.get_full_name }}
                    </a>
                </h3>
                
                <p class="text-sm text-gray-600">
                    {{ talent.professional_title }}
                </p>
                
                <!-- Rating -->
                <div class="flex items-center mt-1">
                    <div class="flex items-center">
                        {% for i in "12345" %}
                        <svg class="w-4 h-4 {% if forloop.counter <= talent.reviews_rating %}text-yellow-400{% else %}text-gray-300{% endif %}" 
                             fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                        </svg>
                        {% endfor %}
                    </div>
                    <span class="ml-2 text-sm text-gray-600">
                        ({{ talent.reviews_count }} reviews)
                    </span>
                </div>
            </div>
            
            <!-- Verification Badge -->
            {% if talent.is_verified %}
            <div class="flex-shrink-0">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                    </svg>
                    Verified
                </span>
            </div>
            {% endif %}
        </div>
        
        <!-- About -->
        <p class="mt-4 text-gray-700 text-sm line-clamp-3">
            {{ talent.about|truncatewords:25 }}
        </p>
        
        <!-- Skills -->
        <div class="mt-4 flex flex-wrap gap-1">
            {% for skill in talent.skills.all|slice:":4" %}
            <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                {{ skill.name }}
            </span>
            {% endfor %}
            
            {% if talent.skills.count > 4 %}
            <span class="text-xs text-gray-500">
                +{{ talent.skills.count|add:"-4" }} more
            </span>
            {% endif %}
        </div>
        
        <!-- Rate and Actions -->
        <div class="mt-4 flex justify-between items-center">
            <div class="text-lg font-semibold text-green-600">
                ${{ talent.rate_hourly }}/hr
            </div>
            
            <div class="flex space-x-2">
                <a href="{% url 'talents:detail' talent.pk %}" 
                   class="btn btn-outline btn-sm">
                    View Profile
                </a>
                
                {% if request.user.is_authenticated and request.user.role == 'client' %}
                <button data-controller="invite-talent"
                        data-action="click->invite-talent#show"
                        data-invite-talent-id-value="{{ talent.id }}"
                        class="btn btn-primary btn-sm">
                    Invite
                </button>
                {% endif %}
            </div>
        </div>
    </div>
</div>
```

## 📱 Responsive Design

### **Mobile-First Approach**
```html
<!-- Responsive navigation -->
<nav class="bg-white shadow-lg" data-controller="mobile-menu">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="{% url 'index' %}" class="flex-shrink-0">
                    <img class="h-8 w-auto" src="{% static 'images/logo.svg' %}" alt="Crelancer">
                </a>
            </div>
            
            <!-- Desktop Navigation -->
            <div class="hidden md:flex md:items-center md:space-x-8">
                <a href="{% url 'jobs:list' %}" class="nav-link">Find Jobs</a>
                <a href="{% url 'talents:list' %}" class="nav-link">Find Talent</a>
                
                {% if user.is_authenticated %}
                    <div class="relative" data-controller="dropdown">
                        <button data-action="click->dropdown#toggle" 
                                class="flex items-center space-x-2">
                            <span>{{ user.get_full_name }}</span>
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                            </svg>
                        </button>
                        
                        <div data-dropdown-target="menu" 
                             class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg">
                            <!-- Dropdown items -->
                        </div>
                    </div>
                {% else %}
                    <a href="{% url 'login' %}" class="btn btn-outline">Sign In</a>
                    <a href="{% url 'signup' %}" class="btn btn-primary">Sign Up</a>
                {% endif %}
            </div>
            
            <!-- Mobile menu button -->
            <div class="md:hidden flex items-center">
                <button data-action="click->mobile-menu#toggle"
                        data-mobile-menu-target="button"
                        class="text-gray-500 hover:text-gray-700">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>
    
    <!-- Mobile menu -->
    <div data-mobile-menu-target="menu" class="hidden md:hidden">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            <!-- Mobile navigation items -->
        </div>
    </div>
</nav>
```

## 🎯 Key Takeaways

1. **Template Inheritance**: Clear hierarchy with base, layout, and page templates
2. **Component Design**: Reusable components for consistent UI
3. **Hotwire Integration**: Turbo Frames and Streams for dynamic updates
4. **Stimulus Controllers**: JavaScript behavior attached to HTML elements
5. **Responsive Design**: Mobile-first approach with TailwindCSS
6. **Performance**: Optimized templates with minimal database queries

## 🔗 What's Next?

Now that you understand templates and Hotwire, let's explore the authentication and user management system.

**Next**: [Lesson 9: Authentication & User Management](./09-authentication.md)

---

## 💡 Quick Quiz

1. What's the difference between Turbo Frames and Turbo Streams?
2. How do Stimulus controllers connect to HTML elements?
3. What's the purpose of template inheritance?
4. How are responsive designs implemented with TailwindCSS?

*Answers: 1) Frames replace entire sections, Streams target specific elements, 2) data-controller attribute, 3) Consistent layout and reduced code duplication, 4) Responsive utility classes like md:hidden, lg:flex*
