# Lesson 13: Webpack Configuration & Asset Management 📦

## 🎯 Learning Objectives
By the end of this lesson, you will understand:
- Webpack configuration for Django projects
- Asset compilation and optimization
- Development vs production builds
- Hot module replacement and live reloading
- Bundle analysis and performance optimization

## 📦 Webpack Configuration Overview

### **Project Structure**
```
frontend/
├── 📁 src/                          # Source files
│   ├── 📄 application.js            # Main entry point
│   ├── 📁 controllers/              # Stimulus controllers
│   ├── 📁 stylesheets/              # SCSS files
│   └── 📁 application/              # Core application files
├── 📁 build/                        # Compiled assets (generated)
├── 📁 vendors/                      # Third-party libraries
└── 📁 webpack/                      # Webpack configurations
    ├── 📄 webpack.config.dev.js     # Development config
    ├── 📄 webpack.config.prod.js    # Production config
    └── 📄 webpack.config.watch.js   # Watch mode config
```

### **Base Webpack Configuration**
```javascript
// frontend/webpack/webpack.config.base.js
const path = require('path')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const { CleanWebpackPlugin } = require('clean-webpack-plugin')

const isDevelopment = process.env.NODE_ENV !== 'production'

module.exports = {
  entry: {
    app: path.resolve(__dirname, '../src/application.js'),
  },
  
  output: {
    path: path.resolve(__dirname, '../build'),
    filename: isDevelopment ? '[name].js' : '[name].[contenthash].js',
    chunkFilename: isDevelopment ? '[name].chunk.js' : '[name].[contenthash].chunk.js',
    publicPath: '/static/',
    clean: true,
  },
  
  resolve: {
    extensions: ['.js', '.jsx', '.ts', '.tsx', '.scss', '.css'],
    alias: {
      '@': path.resolve(__dirname, '../src'),
      '@controllers': path.resolve(__dirname, '../src/controllers'),
      '@stylesheets': path.resolve(__dirname, '../src/stylesheets'),
    }
  },
  
  module: {
    rules: [
      // JavaScript/TypeScript
      {
        test: /\.(js|jsx|ts|tsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              ['@babel/preset-env', {
                targets: {
                  browsers: ['> 1%', 'last 2 versions']
                },
                useBuiltIns: 'entry',
                corejs: 3
              }]
            ],
            plugins: [
              '@babel/plugin-proposal-class-properties',
              '@babel/plugin-syntax-dynamic-import'
            ]
          }
        }
      },
      
      // Stylesheets
      {
        test: /\.(scss|sass|css)$/,
        use: [
          isDevelopment ? 'style-loader' : MiniCssExtractPlugin.loader,
          {
            loader: 'css-loader',
            options: {
              importLoaders: 2,
              sourceMap: isDevelopment
            }
          },
          {
            loader: 'postcss-loader',
            options: {
              postcssOptions: {
                plugins: [
                  require('tailwindcss'),
                  require('autoprefixer'),
                  ...(isDevelopment ? [] : [require('cssnano')])
                ]
              },
              sourceMap: isDevelopment
            }
          },
          {
            loader: 'sass-loader',
            options: {
              sourceMap: isDevelopment
            }
          }
        ]
      },
      
      // Images
      {
        test: /\.(png|jpe?g|gif|svg|webp)$/i,
        type: 'asset',
        parser: {
          dataUrlCondition: {
            maxSize: 8 * 1024 // 8kb
          }
        },
        generator: {
          filename: 'images/[name].[hash][ext]'
        }
      },
      
      // Fonts
      {
        test: /\.(woff|woff2|eot|ttf|otf)$/i,
        type: 'asset/resource',
        generator: {
          filename: 'fonts/[name].[hash][ext]'
        }
      }
    ]
  },
  
  plugins: [
    new CleanWebpackPlugin(),
    
    new MiniCssExtractPlugin({
      filename: isDevelopment ? '[name].css' : '[name].[contenthash].css',
      chunkFilename: isDevelopment ? '[name].chunk.css' : '[name].[contenthash].chunk.css'
    })
  ],
  
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
        stimulus: {
          test: /[\\/]node_modules[\\/]@hotwired[\\/]/,
          name: 'stimulus',
          chunks: 'all',
        }
      }
    }
  }
}
```

### **Development Configuration**
```javascript
// frontend/webpack/webpack.config.dev.js
const { merge } = require('webpack-merge')
const baseConfig = require('./webpack.config.base.js')
const path = require('path')

module.exports = merge(baseConfig, {
  mode: 'development',
  
  devtool: 'eval-source-map',
  
  devServer: {
    host: '0.0.0.0',
    port: 3000,
    hot: true,
    liveReload: true,
    
    // Serve static files
    static: {
      directory: path.join(__dirname, '../build'),
      publicPath: '/static/'
    },
    
    // Proxy API requests to Django
    proxy: {
      '/api': 'http://localhost:8000',
      '/admin': 'http://localhost:8000',
      '/media': 'http://localhost:8000'
    },
    
    // Headers for CORS
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization'
    },
    
    // Overlay errors in browser
    client: {
      overlay: {
        errors: true,
        warnings: false
      }
    },
    
    // Watch options
    watchFiles: [
      'src/**/*',
      '../backend/templates/**/*.html'
    ]
  },
  
  // Fast rebuilds
  cache: {
    type: 'filesystem',
    buildDependencies: {
      config: [__filename]
    }
  }
})
```

### **Production Configuration**
```javascript
// frontend/webpack/webpack.config.prod.js
const { merge } = require('webpack-merge')
const baseConfig = require('./webpack.config.base.js')
const TerserPlugin = require('terser-webpack-plugin')
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin')
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer')

module.exports = merge(baseConfig, {
  mode: 'production',
  
  devtool: 'source-map',
  
  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          compress: {
            drop_console: true,
            drop_debugger: true
          },
          format: {
            comments: false
          }
        },
        extractComments: false
      }),
      
      new CssMinimizerPlugin({
        minimizerOptions: {
          preset: [
            'default',
            {
              discardComments: { removeAll: true }
            }
          ]
        }
      })
    ],
    
    // Better long-term caching
    moduleIds: 'deterministic',
    runtimeChunk: 'single',
    
    splitChunks: {
      chunks: 'all',
      maxInitialRequests: Infinity,
      minSize: 0,
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name(module) {
            // Get the name of the package
            const packageName = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/)[1]
            return `npm.${packageName.replace('@', '')}`
          },
          chunks: 'all'
        }
      }
    }
  },
  
  plugins: [
    // Analyze bundle size (optional)
    ...(process.env.ANALYZE ? [new BundleAnalyzerPlugin()] : [])
  ],
  
  performance: {
    maxAssetSize: 250000,
    maxEntrypointSize: 250000,
    hints: 'warning'
  }
})
```

## 🔧 Asset Pipeline Integration

### **Django Webpack Loader Configuration**
```python
# backend/settings/base.py
WEBPACK_LOADER = {
    'DEFAULT': {
        'CACHE': not DEBUG,
        'BUNDLE_DIR_NAME': 'build/',
        'STATS_FILE': os.path.join(BASE_DIR, 'frontend', 'build', 'webpack-stats.json'),
        'POLL_INTERVAL': 0.1,
        'TIMEOUT': None,
        'IGNORE': [r'.+\.hot-update.js', r'.+\.map'],
        'LOADER_CLASS': 'webpack_loader.loader.WebpackLoader',
    }
}

# Static files configuration
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'frontend', 'build'),
]

STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
```

### **Template Integration**
```html
<!-- backend/templates/base.html -->
{% load webpack_loader %}

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Crelancer{% endblock %}</title>
    
    <!-- Load CSS bundles -->
    {% render_bundle 'app' 'css' %}
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% block content %}{% endblock %}
    
    <!-- Load JavaScript bundles -->
    {% render_bundle 'app' 'js' %}
    
    {% block extra_js %}{% endblock %}
</body>
</html>
```

### **Webpack Stats Plugin**
```javascript
// frontend/webpack/plugins/webpack-stats.js
const fs = require('fs')
const path = require('path')

class WebpackStatsPlugin {
  constructor(options = {}) {
    this.options = {
      filename: 'webpack-stats.json',
      ...options
    }
  }
  
  apply(compiler) {
    compiler.hooks.done.tap('WebpackStatsPlugin', (stats) => {
      const statsData = stats.toJson({
        hash: true,
        publicPath: true,
        assets: true,
        chunks: false,
        modules: false,
        source: false,
        errorDetails: false,
        timings: false
      })
      
      const outputPath = path.join(compiler.options.output.path, this.options.filename)
      
      const data = {
        status: stats.hasErrors() ? 'error' : 'done',
        publicPath: statsData.publicPath,
        assets: statsData.assets.reduce((acc, asset) => {
          const name = asset.name
          const chunks = asset.chunks || []
          
          chunks.forEach(chunk => {
            if (!acc[chunk]) acc[chunk] = []
            acc[chunk].push({
              name: name,
              path: `${statsData.publicPath}${name}`
            })
          })
          
          return acc
        }, {}),
        chunks: statsData.chunks
      }
      
      fs.writeFileSync(outputPath, JSON.stringify(data, null, 2))
    })
  }
}

module.exports = WebpackStatsPlugin
```

## 🎨 PostCSS Configuration

### **PostCSS Config**
```javascript
// postcss.config.js
module.exports = {
  plugins: [
    require('postcss-import'),
    require('tailwindcss'),
    require('autoprefixer'),
    
    // Production optimizations
    ...(process.env.NODE_ENV === 'production' ? [
      require('cssnano')({
        preset: ['default', {
          discardComments: {
            removeAll: true,
          },
          normalizeWhitespace: false,
        }]
      })
    ] : [])
  ]
}
```

### **TailwindCSS Configuration**
```javascript
// tailwind.config.js
const colors = require('tailwindcss/colors')

module.exports = {
  content: [
    './backend/templates/**/*.html',
    './backend/apps/**/templates/**/*.html',
    './frontend/src/**/*.js',
  ],
  
  theme: {
    extend: {
      colors: {
        primary: colors.blue,
        secondary: colors.purple,
        success: colors.green,
        warning: colors.yellow,
        error: colors.red,
      },
      
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
      },
      
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      }
    }
  },
  
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
  ]
}
```

## 🚀 Build Scripts and Automation

### **Package.json Scripts**
```json
{
  "name": "crelancer-frontend",
  "version": "1.0.0",
  "scripts": {
    "build": "cross-env NODE_ENV=production webpack --config frontend/webpack/webpack.config.prod.js",
    "build:analyze": "cross-env NODE_ENV=production ANALYZE=true webpack --config frontend/webpack/webpack.config.prod.js",
    "start": "webpack serve --config frontend/webpack/webpack.config.dev.js",
    "watch": "webpack --watch --config frontend/webpack/webpack.config.watch.js",
    "clean": "rimraf frontend/build",
    "lint": "eslint frontend/src --ext .js,.jsx,.ts,.tsx",
    "lint:fix": "eslint frontend/src --ext .js,.jsx,.ts,.tsx --fix",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage"
  },
  
  "dependencies": {
    "@hotwired/stimulus": "^3.2.1",
    "@hotwired/turbo": "^7.3.0",
    "@stripe/stripe-js": "^2.1.1",
    "@tailwindcss/forms": "^0.5.3",
    "core-js": "^3.20.3",
    "js-cookie": "^3.0.5"
  },
  
  "devDependencies": {
    "@babel/core": "^7.16.7",
    "@babel/preset-env": "^7.16.8",
    "autoprefixer": "^10.4.2",
    "babel-loader": "^8.2.3",
    "clean-webpack-plugin": "^4.0.0",
    "cross-env": "^7.0.3",
    "css-loader": "^6.5.1",
    "css-minimizer-webpack-plugin": "^4.2.2",
    "eslint": "^8.7.0",
    "mini-css-extract-plugin": "^2.5.1",
    "postcss": "^8.4.5",
    "postcss-loader": "^6.2.1",
    "sass": "^1.49.9",
    "sass-loader": "^13.2.0",
    "tailwindcss": "^3.3.2",
    "terser-webpack-plugin": "^5.3.0",
    "webpack": "^5.82.0",
    "webpack-bundle-analyzer": "^4.5.0",
    "webpack-cli": "^4.9.1",
    "webpack-dev-server": "^4.7.3",
    "webpack-merge": "^5.8.0"
  }
}
```

### **Build Automation Script**
```bash
#!/bin/bash
# build-production.sh

set -e

echo "🏗️  Building Crelancer frontend for production..."

# Clean previous builds
echo "🧹 Cleaning previous builds..."
npm run clean

# Install dependencies
echo "📦 Installing dependencies..."
npm ci --production=false

# Lint code
echo "🔍 Linting code..."
npm run lint

# Run tests
echo "🧪 Running tests..."
npm run test

# Build for production
echo "🚀 Building for production..."
npm run build

# Collect Django static files
echo "📁 Collecting Django static files..."
python manage.py collectstatic --noinput

echo "✅ Build completed successfully!"
```

## 📊 Performance Optimization

### **Bundle Analysis**
```javascript
// scripts/analyze-bundle.js
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer')
const webpack = require('webpack')
const config = require('../frontend/webpack/webpack.config.prod.js')

// Add analyzer plugin
config.plugins.push(
  new BundleAnalyzerPlugin({
    analyzerMode: 'server',
    openAnalyzer: true,
    reportFilename: 'bundle-report.html'
  })
)

webpack(config, (err, stats) => {
  if (err || stats.hasErrors()) {
    console.error('Build failed:', err || stats.toString())
    process.exit(1)
  }
  
  console.log('Bundle analysis complete!')
})
```

### **Performance Monitoring**
```javascript
// frontend/src/utils/performance.js
export class PerformanceMonitor {
  static measurePageLoad() {
    if (typeof window !== 'undefined' && window.performance) {
      window.addEventListener('load', () => {
        setTimeout(() => {
          const perfData = window.performance.timing
          const pageLoadTime = perfData.loadEventEnd - perfData.navigationStart
          
          console.log(`Page load time: ${pageLoadTime}ms`)
          
          // Send to analytics
          this.sendMetric('page_load_time', pageLoadTime)
        }, 0)
      })
    }
  }
  
  static measureBundleSize() {
    if (typeof window !== 'undefined') {
      const scripts = document.querySelectorAll('script[src]')
      let totalSize = 0
      
      scripts.forEach(script => {
        fetch(script.src, { method: 'HEAD' })
          .then(response => {
            const size = response.headers.get('content-length')
            if (size) totalSize += parseInt(size)
          })
      })
      
      setTimeout(() => {
        console.log(`Total bundle size: ${(totalSize / 1024).toFixed(2)}KB`)
        this.sendMetric('bundle_size', totalSize)
      }, 1000)
    }
  }
  
  static sendMetric(name, value) {
    // Send to your analytics service
    if (window.gtag) {
      window.gtag('event', 'performance_metric', {
        metric_name: name,
        metric_value: value
      })
    }
  }
}

// Initialize monitoring
PerformanceMonitor.measurePageLoad()
PerformanceMonitor.measureBundleSize()
```

## 🎯 Key Takeaways

1. **Modular Configuration**: Separate configs for development and production
2. **Asset Optimization**: Minification, compression, and code splitting
3. **Development Experience**: Hot reloading and source maps
4. **Django Integration**: Seamless asset loading with webpack-loader
5. **Performance Monitoring**: Bundle analysis and optimization
6. **Build Automation**: Scripts for consistent deployments

## 🔗 What's Next?

Now that you understand the complete frontend architecture, let's dive into the core features of Crelancer, starting with user registration and verification.

**Next**: [Lesson 14: User Registration & Verification](../04-features/14-user-registration.md)

---

## 💡 Quick Quiz

1. What's the difference between development and production webpack configs?
2. How does webpack-loader integrate with Django templates?
3. What's the purpose of code splitting in webpack?
4. How do you analyze bundle size and performance?

*Answers: 1) Dev has source maps and hot reloading, prod has minification and optimization, 2) render_bundle template tag loads compiled assets, 3) Split code into smaller chunks for better caching and loading, 4) Bundle analyzer plugin and performance monitoring scripts*
