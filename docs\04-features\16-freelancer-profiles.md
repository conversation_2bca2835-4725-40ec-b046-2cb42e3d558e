# Lesson 16: Freelancer Profiles & Portfolios 👨‍💻

## 🎯 Learning Objectives
- Talent profile structure and management
- Portfolio system implementation
- Skills and experience tracking
- Profile visibility and verification
- Client discovery features

## 👤 Talent Profile Model

```python
class Talent(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    
    # Profile status
    is_verified = models.BooleanField(default=False)
    is_published = models.BooleanField(default=False)
    
    # Professional info
    professional_title = models.CharField(max_length=255)
    about = models.TextField()
    skills = SortedManyToManyField(Skill, through="TalentSkill")
    categories = models.ManyToManyField(Category)
    
    # Work details
    rate_hourly = models.DecimalField(max_digits=10, decimal_places=2)
    hours_weekly = models.PositiveIntegerField()
    experience_years = models.PositiveIntegerField()
    
    # Performance metrics
    reviews_rating = models.DecimalField(max_digits=3, decimal_places=2, default=0)
    reviews_count = models.PositiveIntegerField(default=0)
    jobs_completed = models.PositiveIntegerField(default=0)
    
    # Availability
    is_available = models.BooleanField(default=True)
    next_available_date = models.DateField(null=True)
```

## 🎨 Portfolio System

### **Project Model**
```python
class Project(models.Model):
    talent = models.ForeignKey(Talent, on_delete=models.CASCADE, related_name="projects")
    title = models.CharField(max_length=255)
    description = models.TextField()
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    
    # Media
    image = ResizedImageField(upload_to="portfolio/images/", size=[1200, 800])
    thumbnail = ResizedImageField(upload_to="portfolio/thumbs/", size=[400, 300])
    
    # Project details
    technologies_used = models.ManyToManyField(Skill)
    project_url = models.URLField(blank=True)
    github_url = models.URLField(blank=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    is_featured = models.BooleanField(default=False)
    sort_order = models.PositiveIntegerField(default=0)
```

### **Portfolio Management View**
```python
class PortfolioManageView(TalentRequiredMixin, TemplateView):
    template_name = "portfolio/manage.html"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        talent = self.request.user.talent
        
        context['projects'] = talent.projects.all().order_by('sort_order', '-created_at')
        context['featured_projects'] = talent.projects.filter(is_featured=True)
        context['categories'] = Category.objects.all()
        
        return context
```

## 🔍 Talent Discovery

### **Talent Search View**
```python
class TalentSearchView(ListView):
    model = Talent
    template_name = "talents/search.html"
    paginate_by = 12
    
    def get_queryset(self):
        queryset = Talent.objects.available()
        
        # Search by skills
        skills = self.request.GET.getlist('skills')
        if skills:
            queryset = queryset.filter(skills__id__in=skills).distinct()
        
        # Filter by category
        category = self.request.GET.get('category')
        if category:
            queryset = queryset.filter(categories__slug=category)
        
        # Rate range
        min_rate = self.request.GET.get('min_rate')
        max_rate = self.request.GET.get('max_rate')
        if min_rate:
            queryset = queryset.filter(rate_hourly__gte=min_rate)
        if max_rate:
            queryset = queryset.filter(rate_hourly__lte=max_rate)
        
        # Experience level
        experience = self.request.GET.get('experience')
        if experience:
            if experience == 'junior':
                queryset = queryset.filter(experience_years__lt=3)
            elif experience == 'mid':
                queryset = queryset.filter(experience_years__gte=3, experience_years__lt=7)
            elif experience == 'senior':
                queryset = queryset.filter(experience_years__gte=7)
        
        # Availability
        available_only = self.request.GET.get('available_only')
        if available_only:
            queryset = queryset.filter(is_available=True)
        
        # Sorting
        sort_by = self.request.GET.get('sort', 'rating')
        if sort_by == 'rating':
            queryset = queryset.order_by('-reviews_rating', '-reviews_count')
        elif sort_by == 'rate_low':
            queryset = queryset.order_by('rate_hourly')
        elif sort_by == 'rate_high':
            queryset = queryset.order_by('-rate_hourly')
        elif sort_by == 'experience':
            queryset = queryset.order_by('-experience_years')
        
        return queryset.select_related('user')
```

## 📈 Profile Analytics

### **Profile Performance Tracking**
```python
class TalentAnalyticsView(TalentRequiredMixin, TemplateView):
    template_name = "talents/analytics.html"
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        talent = self.request.user.talent
        
        # Profile views
        context['profile_views'] = ProfileView.objects.filter(
            talent=talent,
            created_at__gte=timezone.now() - timedelta(days=30)
        ).count()
        
        # Application success rate
        total_applications = Deal.objects.filter(talent=talent).count()
        hired_applications = Deal.objects.filter(
            talent=talent,
            status=Deal.StatusChoices.ACTIVE
        ).count()
        
        context['success_rate'] = (
            (hired_applications / total_applications * 100) 
            if total_applications > 0 else 0
        )
        
        # Earnings this month
        context['monthly_earnings'] = Transaction.objects.filter(
            deal__talent=talent,
            transaction_type=Transaction.TransactionType.PAYOUT,
            status=Transaction.Status.COMPLETED,
            created_at__month=timezone.now().month
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        return context
```

## 🎯 Key Takeaways

1. **Comprehensive Profiles**: Skills, experience, rates, and availability
2. **Portfolio System**: Project showcase with media and technology tags
3. **Advanced Search**: Multiple filters for talent discovery
4. **Performance Tracking**: Analytics for profile optimization
5. **Verification System**: Trust and credibility indicators

## 🔗 What's Next?

**Next**: [Lesson 17: Deal & Contract System](./17-deals-contracts.md)
