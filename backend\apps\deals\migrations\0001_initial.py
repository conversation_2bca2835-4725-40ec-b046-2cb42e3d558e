# Generated by Django 3.2.18 on 2023-08-21 11:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('clients', '0003_auto_20230719_1539'),
        ('jobs', '0001_squashed_0005_auto_20230811_1315'),
        ('talents', '0017_remove_talentverification_pf'),
    ]

    operations = [
        migrations.CreateModel(
            name='Deal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('dialog', 'Dialog'), ('proposal', 'Proposal'), ('progress', 'Progress'), ('complete', 'Complete')], default='dialog', max_length=16)),
                ('last_message_at', models.DateTimeField(auto_now_add=True)),
                ('client_unread_count', models.PositiveIntegerField(default=0)),
                ('talent_unread_count', models.PositiveIntegerField(default=0)),
                ('client', models.ForeignKey(help_text='Denormalized field for faster access', on_delete=django.db.models.deletion.CASCADE, to='clients.client')),
                ('job', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='jobs.job')),
                ('talent', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='talents.talent')),
            ],
            options={
                'ordering': ['-last_message_at'],
            },
        ),
        migrations.AddIndex(
            model_name='deal',
            index=models.Index(fields=['status'], name='deals_deal_status_06cb7b_idx'),
        ),
        migrations.AddIndex(
            model_name='deal',
            index=models.Index(fields=['last_message_at'], name='deals_deal_last_me_9829c4_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='deal',
            unique_together={('job', 'talent')},
        ),
    ]
